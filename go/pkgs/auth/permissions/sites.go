package permissions

import (
	"contentmanager/library/shared/result"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/utils/slicexx"
	uuid "github.com/satori/go.uuid"
	"time"
)

type (
	Params struct {
		SiteID         *uuid.UUID  `binding:"required"`
		ContentType    string      `binding:"required"`
		ParentSitesIDs []uuid.UUID `json:"ParentSitesIDs[]"`
		IgnoreSiteID   bool
	}

	SiteForSelector struct {
		ID                 uuid.UUID
		Name               string
		PrimaryDomain      string
		Tags               []uuid.UUID
		RestrictedByParent bool
	}

	SiteForAccount struct {
		ID            uuid.UUID   `json:"id"`
		TenantID      uuid.UUID   `json:"tenantId"`
		Name          string      `json:"name"`
		PrimaryDomain string      `json:"primaryDomain"`
		Description   string      `json:"description"`
		Type          string      `json:"type"`
		Active        bool        `json:"active"`
		Created       time.Time   `json:"created"`
		Tags          []uuid.UUID `json:"tags"`
		Hosts         []uuid.UUID `json:"hosts"`
		Departments   []uuid.UUID `json:"departments"`

		// TODO: remove all bellow fields
		IsAdmin                    bool `json:"isAdmin"`
		HasSharedPages             bool `json:"hasSharedPages"`
		HasSitePages               bool `json:"hasSitePages"`
		HasSharedResources         bool `json:"hasSharedResources"`
		HasSiteResources           bool `json:"hasSiteResources"`
		HasSharedNews              bool `json:"hasSharedNews"`
		HasSiteNews                bool `json:"hasSiteNews"`
		HasSharedEvents            bool `json:"hasSharedEvents"`
		HasSiteEvents              bool `json:"hasSiteEvents"`
		HasSharedMedia             bool `json:"hasSharedMedia"`
		HasSiteMedia               bool `json:"hasSiteMedia"`
		HasSharedAlerts            bool `json:"hasSharedAlerts"`
		HasSiteAlerts              bool `json:"hasSiteAlerts"`
		HasTransportationAdmin     bool `json:"hasTransportationAdmin"`
		HasTransportationCanUpdate bool `json:"hasTransportationCanUpdate"`
		HasTags                    bool `json:"hasTags"`
		HasAccounts                bool `json:"hasAccounts"`
		HasSiteSettings            bool `json:"hasSiteSettings"`
		HasSettingsInstagram       bool `json:"hasSettingsInstagram"`
	}
	SiteViewModel struct {
		ID            uuid.UUID
		TenantID      uuid.UUID
		Name          string
		PrimaryDomain string
		Description   string
		Type          string
		Active        bool
		Created       time.Time
		Tags          []uuid.UUID
		Hosts         []uuid.UUID
	}
)

func GetSitesForTenant(sites []tenancyModels.Site) result.Result[[]SiteViewModel] {
	vms := slicexx.Select(sites, func(s tenancyModels.Site) SiteViewModel {
		return SiteViewModel{
			ID:            s.ID,
			TenantID:      s.TenantID,
			Name:          s.Name,
			PrimaryDomain: s.PrimaryDomain,
			Description:   s.Description,
			Type:          string(s.Type),
			Active:        s.Active,
			Created:       s.Created,
			Tags:          s.Tags,
			Hosts:         slicexx.Select(s.Hosts, func(h tenancyModels.Site) uuid.UUID { return h.ID }),
		}
	})
	return result.Success(vms)
}

// mapSitesToResult maps sites to SiteForSelector and returns result. Returns only regular sites (not departments)
// and limits list by parentIDs if they are not empty
func mapSitesToResult(sites []tenancyModels.Site, parentIDs []uuid.UUID) result.Result[[]SiteForSelector] {
	// remove departments, only sites in site selector for content
	filtered := slicexx.Filter(sites, func(s tenancyModels.Site) bool {
		return !s.IsDepartment()
	})

	res := slicexx.Select(filtered, func(s tenancyModels.Site) SiteForSelector {
		return SiteForSelector{
			ID:                 s.ID,
			Name:               s.Name,
			PrimaryDomain:      s.PrimaryDomain,
			Tags:               s.Tags,
			RestrictedByParent: len(parentIDs) > 0 && !slicexx.Contains(parentIDs, s.ID),
		}
	})
	return result.Success(res)
}
