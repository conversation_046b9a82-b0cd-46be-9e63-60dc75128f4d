package google

import (
	"contentmanager/etc/conf"
	"contentmanager/library/binding"
	"contentmanager/library/shared"
	"contentmanager/library/utils/mapxx"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/googleapi"
	"context"
	"errors"
	"fmt"
	"google.golang.org/api/idtoken"
	"gorm.io/gorm"
	"net/http"
)

type (
	ResponseForm struct {
		IdToken string `json:"id_token"`
	}
)

func Authenticate(r *shared.AppContext, identityCertificate, email string, manager token.TokenManager[identity.PublicAccount]) (string, error) {
	adminUser, err := r.GoogleAdminByEmail(email)
	if err != nil {
		return "", fmt.Errorf("[GoogleAdminByEmail]: %s", err.Error())
	}
	account, err := buildAccountSecurityContext(r.TenantDatabase(), identityCertificate, adminUser, email)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("[buildAccountSecurityContext]: %s]", err.<PERSON>rror())
	}
	claim, errClaim := manager.CreateToken(context.Background(), identity.PublicAccount{
		ID:           account.ID,
		Name:         account.Firstname + " " + account.Lastname,
		Email:        account.Email,
		PrivacyLevel: account.PrivacyLevel,
	})

	return claim, errClaim
}

func GetEmailFromResponseForm(r http.Request) (string, error) {
	var responseForm ResponseForm
	if err := binding.MapFromMaps(&responseForm, r.PostForm); err != nil {
		return "", err
	}
	payload, err := idtoken.Validate(r.Context(), responseForm.IdToken, conf.GoogleAudience)
	if err != nil {
		return "", err
	}
	email := mapxx.GetAsString(payload.Claims, "email")
	if len(email) == 0 {
		return "", errors.New("expected to find email within claims")
	}
	return email, nil
}

func buildAccountSecurityContext(db *gorm.DB, identityCertificate string, adminUser string, userEmail string) (identity.Account, error) {
	userSvc := googleapi.User{}

	googleUser, err := userSvc.GetUser(identityCertificate, adminUser, userEmail)
	if err != nil {
		return identity.Account{}, err
	}
	if googleUser == nil {
		return identity.Account{}, errors.New("user cannot be reconciled")
	}
	external := login.ExternalAccount{
		Email:          googleUser.PrimaryEmail,
		FirstName:      googleUser.Name.GivenName,
		LastName:       googleUser.Name.FamilyName,
		ExternalGroups: []string{},
	}

	userPrimaryOrgUnit, _ := userSvc.GetUserOrganization(identityCertificate, googleUser.CustomerId, adminUser, userEmail, googleUser.OrgUnitPath[1:])
	if userPrimaryOrgUnit != nil && len(userPrimaryOrgUnit.OrgUnitId) > 0 {
		external.ExternalGroups = []string{userPrimaryOrgUnit.OrgUnitId}
	}
	GoogleGroupSvc := googleapi.Group{}
	userGroups, _ := GoogleGroupSvc.GetUserGroups(identityCertificate, adminUser, userEmail)
	if userGroups != nil && userGroups.Groups != nil {
		for _, group := range userGroups.Groups {
			external.ExternalGroups = append(external.ExternalGroups, group.Id)
		}
	}
	return login.UpsertExternalAccount(db, external)
}
