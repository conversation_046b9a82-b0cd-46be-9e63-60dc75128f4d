package publicCalendar

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenant/public/features/content"
	"contentmanager/library/utils"
	"contentmanager/pkgs/content/events"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"strconv"
	"time"
)

// PublicCalendarEvents_GET
// Current implementation mostly keeps existing functionality to save time & for backwards compatibility.
// Notes:
// - Known Necessary fields kept & in same type (e.g uuid.NullUUID) for reason above.
// - Content field is omitted but present on the PublicCalendarEvent model (will always be "")
// - Previously controller never returned errors, this emulates that behaviour.
// Useful future additions:
// - refactor current implementation or use something like binding pkg
// - sort / order by, specifically, order by start date - this will help with new isAllDay paradigm
func PublicCalendarEvents_GET(w http.ResponseWriter, r *shared.AppContext) {
	var params = PublicCalendarParams{
		siteId:       r.CurrentSiteID(),
		privacyLevel: r.PublicAccount().PrivacyLevel, // TODO: privacyLevel should come from AppContext, not from params, so GetPublicCalendarEvents should depend on AppContext
	}
	r.Request().ParseForm()

	if r.Request().Form["startdate"] != nil {
		if StartDate, StartDateErr := time.Parse(time.RFC3339Nano, r.Request().Form["startdate"][len(r.Request().Form["startdate"])-1]); StartDateErr == nil {
			params.startDate = StartDate
		} else {
			params.startDate = time.Now().Truncate(time.Hour).Truncate(time.Minute).Truncate(time.Second)
		}
	} else {
		params.startDate = time.Now().Truncate(time.Hour).Truncate(time.Minute).Truncate(time.Second)
	}

	if r.Request().Form["enddate"] != nil {
		EndDate, EndDateErr := time.Parse(time.RFC3339Nano, r.Request().Form["enddate"][len(r.Request().Form["enddate"])-1])
		if EndDateErr == nil {
			params.endDate = EndDate
		} else {
			params.endDate = params.startDate.Add(time.Hour * 24 * 30)
		}
	} else {
		params.endDate = params.startDate.Add(time.Hour * 24 * 30)
	}

	if r.Request().Form["tags"] != nil {
		params.tags = r.Request().Form["tags"]
		for _, t := range params.tags {
			if t == "uncategorized" {
				params.uncategorized = true
			}
		}
	}
	if a := r.Request().Form.Get("all"); a != "" {
		params.all, _ = strconv.ParseBool(a)
	} else if r.Request().Form["tags"] == nil && r.Request().Form.Get("uncategorized") == "" {
		params.all = true
	}

	if err := events.ValidateTimeRange(params.startDate, params.endDate); err != nil {
		r.Logger().Err(err).Msgf("[PublicCalendarEvents_GET] Invalid time range: %s - %s", params.startDate, params.endDate)
		params.startDate, params.endDate = events.GetMonthRange()
	}

	// This handles full calendar necessary functionality
	// a month shows days before and after it's selected month
	// without the following, we only get "Selected Months" events, which mean that the Last few and First few days
	// of the month before and empty are going to show empty.
	params.startDate = params.startDate.Add(-1 * time.Hour * 24 * 8)
	params.endDate = params.endDate.Add(time.Hour * 24 * 8)

	if c := r.Request().Form.Get("expirationState"); len(c) > 0 {
		params.expirationState = publiccontent.ToExpirationState(c)
	}
	if skip := r.Request().Form.Get("skipExpand"); len(skip) > 0 && skip == "true" {
		params.skipExpand = true
	}
	if chk := r.Request().Form.Get("includeSites"); len(chk) > 0 {
		params.includeSites = true
	}
	if r.Request().Form["excludeExternalIds"] != nil {
		params.excludeExternalIds = r.Request().Form["excludeExternalIds"]
	}
	if r.Request().Form["externalIds"] != nil {
		params.externalIds = r.Request().Form["externalIds"]
	}

	calendarEvents, err := GetPublicCalendarEvents(r, params)
	if err != nil {
		r.Logger().Err(err).Msg("Failed to get public calendar events")
		utils.WriteError(w, err)
		return
	}

	if !params.includeSites {
		for i, _ := range calendarEvents {
			calendarEvents[i].Sites = []uuid.UUID{}
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(calendarEvents)
}
