package routes

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/claims"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login/google"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"net/http"
)

// TODO => MT: Test between social login and this one.
func GoogleLoginHandler(w http.ResponseWriter, r *shared.AppContext, manager token.TokenManager[identity.PublicAccount]) {
	email, err := google.GetEmailFromResponseForm(*r.Request())
	if err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("Error decoding Google response")
		utils.WriteStatusJSON(w, http.StatusBadRequest, map[string]interface{}{"Error": "Error decoding Google response", "ErrorMessage": err.Error(), "ErrorData": err})
	}
	appConfig := config.GetAppConfig()
	jwt, err := google.Authenticate(r, appConfig.GCloudIdentityCertificate, email, manager)
	if err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusUnauthorized)
		return
	}

	claims.SetCookies(w, jwt, r.Request().Host)
	utils.ResponseJson(w, utils.Message("success"), http.StatusOK)
}
