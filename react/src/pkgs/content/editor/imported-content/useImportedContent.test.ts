import { describe, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useImportedContent, useImportedContentProps } from './useImportedContent'
import { test_tenantImportConfigurations } from './test_data'

const expectedResults = {
    allDisabled: { published: false, privacyLevel: false, site: false, tags: false },
    allEnabled: { published: true, privacyLevel: true, site: true, tags: true },
    publishedAndPrivacyLevelOK: { published: true, privacyLevel: true, site: false, tags: false },
    siteAndTagsOK: { published: false, privacyLevel: false, site: true, tags: true }
}

/* Suggestion: Instead of providing a list of Enabled / Disabled like above, we should return a list of Disabled fields.
    e.g instead of { published: true, privacyLevel: true, site: true, tags: true }
    return : [site, tags]
*/

describe('useImportedContent', () => {
    test('Should return isFieldEnabled and null importInfo when given empty Content', () => {
        const incompleteProps: useImportedContentProps = {
            content: undefined,
            tenantImportConfigurations: test_tenantImportConfigurations
        }
        const { result } = renderHook((props: useImportedContentProps) => useImportedContent(props || incompleteProps))
        expect(result.current.importInfo).toBeNull()
        expect(typeof result.current.isFieldEnabled).toBe('function')
    })
    test('Should return importInfo as null while props are loading and defined when loaded', () => {
        const incompleteProps: useImportedContentProps = {
            content: undefined,
            tenantImportConfigurations: test_tenantImportConfigurations
        }
        const { result, rerender } = renderHook((props: useImportedContentProps) =>
            useImportedContent(props || incompleteProps)
        )
        expect(result.current.importInfo).toBeNull()
        expect(result.current.isFieldEnabled).toBeDefined()

        rerender({
            content: {
                Type: 'news',
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            tenantImportConfigurations: test_tenantImportConfigurations
        })

        expect(result.current.importInfo).toBeTruthy()
    })
    test('Should return editable fields from site config', () => {
        const initialProps: useImportedContentProps = {
            content: {
                Type: 'news',
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            tenantImportConfigurations: test_tenantImportConfigurations
        }
        const { result, rerender } = renderHook((props: useImportedContentProps) =>
            useImportedContent(props || initialProps)
        )
        expect(result.current.importInfo).toBeDefined()
        let { source = '', editableFieldsSet = [] } = result.current.importInfo || {}
        expect(source).toBe('outlook')
        expect([...editableFieldsSet]).toMatchObject(['published', 'privacyLevel'])

        rerender({
            content: {
                Type: 'event',
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'siteAndTagsEnabled'
                    }
                }
            },
            tenantImportConfigurations: test_tenantImportConfigurations
        })

        expect(result.current.importInfo).toBeTruthy()
        expect(result.current.importInfo?.source).toBe('outlook')
        expect([...(result.current.importInfo?.editableFieldsSet as Set<string>)]).toMatchObject(['site', 'tags'])
    })
    test('Should be able to determine enabled fields from config', () => {
        const initialProps: useImportedContentProps = {
            content: {
                Type: 'news',
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            tenantImportConfigurations: test_tenantImportConfigurations
        }
        const { result } = renderHook((props: useImportedContentProps) => useImportedContent(props || initialProps))
        expect(result.current.isFieldEnabled).toBeDefined()
        const inputState = test_getEnabledState(result.current.isFieldEnabled)
        expect(inputState).toMatchObject(expectedResults.publishedAndPrivacyLevelOK)
    })
})

function test_getEnabledState(isFieldEnabledFunc: (name: string) => boolean) {
    return {
        published: isFieldEnabledFunc('published'),
        privacyLevel: isFieldEnabledFunc('privacyLevel'),
        site: isFieldEnabledFunc('site'),
        tags: isFieldEnabledFunc('tags')
    }
}
