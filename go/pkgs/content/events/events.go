package events

import (
	"contentmanager/library/shared"
	"gorm.io/gorm"
	"strings"
	"time"
)

const template = "" +
	"({isAllDay} = true AND @startUTC < {end})" + // all-day events
	" OR " +
	"({isAllDay} = false AND @startLocal <= {end})" // events with time

var upcomingQuery = replace(template)

// GetEventsQuery returns a query for events.
// If period is "all", it returns all events.
// Otherwise, it returns upcoming events or events that happen in the current day.
func GetEventsQuery(r *shared.AppContext, db *gorm.DB, period string) (*gorm.DB, error) {
	period = strings.ToLower(period)

	tx := db.Where("type = 'event'")
	if period == "all" {
		return tx, nil
	}

	loc, err := time.LoadLocation(r.Timezone())
	if err != nil {
		return nil, err
	}

	now := time.Now().In(loc)

	startOfDayLocal := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
	startOfDayUTC := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)

	tx = tx.Where(upcomingQuery, map[string]interface{}{"startUTC": startOfDayUTC, "startLocal": startOfDayLocal})

	return tx, nil
}

func replace(tpl string) string {
	m := map[string]string{
		"{isAllDay}": "coalesce((content.settings->>'isAllDay')::bool, false)",
		"{start}":    "(content.settings->>'startdate')::timestamp with time zone",
		"{end}":      "(content.settings->>'enddate')::timestamp  with time zone",
	}
	for k, v := range m {
		tpl = strings.ReplaceAll(tpl, k, v)
	}
	return tpl
}
