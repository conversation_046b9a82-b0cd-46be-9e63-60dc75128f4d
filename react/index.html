<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />

    <!-- Google tag (gtag.js) -->
    <script>
      if(!window.location.hostname.includes('localhost')) {
        (function(){
          const gaScript = document.createElement('script');
          gaScript.async = true;
          gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-34X83QPNZR';
          gaScript.onload = function() {
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            window.gtag = gtag;
            gtag('js', new Date());
            gtag('config', 'G-34X83QPNZR');

            window.onerror = function(message, source, lineno, colno, error) {
              gtag('event', 'javascript_error', {
                error_message: message,
                error_source: source,
                error_line: lineno,
                error_column: colno,
                error_stack: trimErrorStack(error?.stack)
              })
            };

            window.addEventListener('unhandledrejection', function(event) {
              gtag('event', 'unhandled_promise_rejection', {
                error_message: event.reason?.message || 'Unknown promise rejection',
                error_stack: trimErrorStack(event.reason?.stack)
              });
            });
          }

          document.head.appendChild(gaScript);
        })()
      }


      function trimErrorStack(stack, baseUrl = window.location.origin) {
        if (!stack) return '';

        // Split stack into lines
        const lines = stack.split('\n');

        const [firstLine, ...stackLines] = lines;
        const relevantLines = stackLines.filter(line => {
          return line.includes(baseUrl);
        });

        // Rebuild stack with only relevant lines
        return [firstLine, ...relevantLines].join('\n').trim();
      }
    </script>

    <link rel="icon" href="/favicon.svg" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="Content Manager"
      content="Content Management tool by Imagine Everything"
    />
    <meta name="google-signin-client_id" content="281757022600-vb7n2t80ib020r6v4v2o398lt1kkr6l6.apps.googleusercontent.com">
    <script src="https://apis.google.com/js/platform.js" async defer></script>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>
    <title>Content Manager</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
