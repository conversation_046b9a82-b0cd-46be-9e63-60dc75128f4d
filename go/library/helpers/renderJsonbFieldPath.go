package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"encoding/json"
	"reflect"
	"strings"
)

func init() {
	handlebars.RegisterHelper("renderJsonbFieldPath", renderJsonbFieldPath)

	handlebars.RegisterHelper("jsonRender", renderJsonbFieldPath)
}

func renderJsonbFieldPath(value json.RawMessage, path string, options *handlebars.Options) string {
	var ok bool
	var result map[string]interface{}
	var finalValue string

	if value == nil {
		return ""
	}

	json.Unmarshal(value, &result)
	pathArray := strings.Split(path, ".")

	for _, item := range pathArray[:len(pathArray)-1] {
		result, ok = result[item].(map[string]interface{})
		if !ok {
			return ""
		}
	}
	item := pathArray[len(pathArray)-1]
	v := reflect.ValueOf(result[item])
	r := v.Kind()
	if r != reflect.Invalid && v.Type().String() == "[]interface {}" {
		for _, x := range result[item].([]interface{}) {
			finalValue += options.FnWith(x)
		}
	} else {
		finalValue, ok = result[item].(string)
		if !ok {
			return ""
		}
	}
	return finalValue
}
