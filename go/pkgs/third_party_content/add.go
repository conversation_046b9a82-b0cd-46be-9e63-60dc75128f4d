package third_party_content

import (
	"contentmanager/etc/conf"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/logging"
	"encoding/json"
	"golang.org/x/crypto/bcrypt"
	"net/http"
)

func AddPublic(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Get(conf.RouterAnonymousPassthrough, bindauth.BindParamsMiddleware(), func(res http.ResponseWriter, r *shared.AppContext, params struct {
		bindauth.BindableParams
		FromQuery struct {
			Token string
			Url   string
		}
	}) {
		if tokenErr := bcrypt.CompareHashAndPassword([]byte(params.FromQuery.Token), []byte(conf.BcryptUUIDTemplate)); tokenErr != nil {
			http.Error(res, "unauthorized", http.StatusUnauthorized)
			return
		}
		result, err := GetPublicThirdPartyContent(r.TenantDatabase(), r.<PERSON>(), params.FromQuery.Url)
		if err != nil {
			http.NotFound(res, r.Request())
			return
		}
		if err := json.NewEncoder(res).Encode(result); err != nil {
			logging.RootLogger().Err(err).Msg("failed to encode third_party_content")
		}
	})
	r.Get("api/v2/lookup", bindauth.BindParamsMiddleware(), func(res http.ResponseWriter, r *shared.AppContext, params struct {
		bindauth.BindableParams
		FromQuery struct {
			Token    string
			Resource string
		}
	}) {
		if tokenErr := bcrypt.CompareHashAndPassword([]byte(params.FromQuery.Token), []byte(conf.BcryptUUIDTemplate)); tokenErr != nil {
			http.Error(res, "unauthorized", http.StatusUnauthorized)
			return
		}
		result, err := GetPublicThirdPartyContent(r.TenantDatabase(), r.CurrentSiteID(), params.FromQuery.Resource)
		if err != nil {
			http.NotFound(res, r.Request())
			return
		}
		if err := json.NewEncoder(res).Encode(result); err != nil {
			logging.RootLogger().Err(err).Msg("failed to encode third_party_content")
		}
	})
	r.Get(conf.RouterTwitter, respondWithEmpty)
	r.Get(conf.RouterInstagram, respondWithEmpty)
	return r
}

func respondWithEmpty(res http.ResponseWriter) {
	if _, err := res.Write([]byte("{}")); err != nil {
		logging.RootLogger().Err(err).Msg("error writing empty response")
	}
}
