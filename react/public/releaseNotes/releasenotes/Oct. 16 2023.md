# Release Notes (Oct. 16 2023)

### What's new?

#### Event date and time picker improvements

We've updated the look and feel of the date and time picker when creating and editing events to improve ease of use and
be more in line with the rest of the application.

![Event date time picker](/releaseNotes/event-date-time-picker.png)

-   The end date time picker dropdown will include time past relative to the start time in minutes or hours. Time past
    is only visible if the end date has not yet been selected or if the end date is within 24 hours of the start date.
-   All other date and time pickers will eventually be migrated to this new version

### Bug fixes

-   Fixed PDFs not displaying properly in documents manager
-   Replacing documents should now update the last updated time displayed in documents manager
-   Cloning a page via the page manager no longer copies the primary pin
-   In the WYSIWIG editor, the move controls may move outside the editor when scrolling horizontally. The move controls
    should now always be visible in the center of the editor. When scrolling vertically, the controls will be less
    visible.
-   Scroll to top button should now also appear in the Image Gallery
