package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/olm"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func CreateDistributedLists(r *shared.AppContext, dto ListDTO) result.Result[int] {
	if !dto.Distributed || len(dto.Sites) > 0 {
		return result.Error(errors.New("Non-Distributed lists are not supported on this endpoint. "), 0)
	}

	sites, err := r.Sites()
	if err != nil {
		return result.Error(err, 0)
	}

	now := r.AppTime().NowUTC()
	var ll []olm.List
	var siteIDs []uuid.UUID

	for _, site := range sites {
		if site.IsDepartment() {
			continue
		}

		siteIDs = append(siteIDs, site.ID)

		list := olm.List{
			Base:             dto.Base,
			Name:             dto.Name,
			Description:      dto.Description,
			StructureID:      dto.StructureID,
			OverrideSections: dto.OverrideSections,
			ContentTypes:     dto.ContentTypes,
			Template:         dto.Template,
			Tags:             dto.Tags,
			Active:           true,
			Distributed:      true,
		}
		list.Sites = []uuid.UUID{site.ID}

		if len(dto.Description) == 0 {
			list.Description = fmt.Sprintf("%s distributed list for site %s", dto.Name, site.Name)
		}

		list.Track(now, r.Account().ID)
		ll = append(ll, list)
	}

	check := ListDTO{
		Name: dto.Name,
		Base: content.Base{
			SharableBase: auth.SharableBase{
				Sites:        siteIDs,
				DepartmentID: nil,
			},
		},
	}

	if err := uniqueByNameAndSites(r, check, uuid.Nil); err != nil {
		return result.Error(err, 0)
	}

	if err := r.TenantDatabase().Create(&ll).Error; err != nil {
		return result.Error(err, 0)
	}

	onListChanged(r, ll...)

	return result.Success(len(ll))
}

func UpdateDistributedLists(r *shared.AppContext, dto ListDTO) result.Result[int64] {
	if !dto.Distributed || len(dto.Sites) > 0 {
		return result.Error(errors.New("Non-Distributed lists are not supported on this endpoint. "), int64(0))
	}

	now := r.AppTime().NowUTC()
	if res := r.TenantDatabase().Model(&olm.List{}).Where("distributed and name = ?", dto.Name).Updates(map[string]interface{}{
		"privacy_level": dto.PrivacyLevel,
		"publish_at":    dto.PublishAt,
		"expire_at":     dto.ExpireAt,
		"template":      dto.Template,
		"tags":          dto.Tags,
		"updated_at":    now,
		"updated_by":    r.Account().ID,
	}); res.Error != nil {
		onChangedDistributed(r, dto.Name)
		return result.Error(res.Error, int64(0))
	} else {
		return result.Success(res.RowsAffected)
	}
}

func SetDistributedListsActive(r *shared.AppContext, name string, active bool) result.Result[int64] {
	now := r.AppTime().NowUTC()
	if res := r.TenantDatabase().Model(&olm.List{}).Where("distributed and name = ?", name).Updates(map[string]interface{}{
		"active":     active,
		"updated_at": now,
		"updated_by": r.Account().ID,
	}); res.Error != nil {
		onChangedDistributed(r, name)
		return result.Error(res.Error, int64(0))
	} else {
		return result.Success(res.RowsAffected)
	}
}

func AddItemToDistribution(r *shared.AppContext, name string, item ItemDTO) result.Result[int64] {
	var results []olm.List
	now := r.AppTime().NowUTC()

	batchesResult := r.TenantDatabase().Model(&olm.List{}).Where("distributed and name = ?", name).
		FindInBatches(&results, 100, func(tx *gorm.DB, batch int) error {

			for i, _ := range results {
				if slicexx.Any(results[i].Items, func(itemInList olm.Item) bool {
					return itemInList.ContentID == item.ContentID
				}) {
					continue
				}

				results[i].Items = append(results[i].Items, olm.Item{
					ContentID: item.ContentID,
					Overrides: item.Overrides,
				})
				results[i].Track(now, r.Account().ID)
			}

			if err := tx.Save(&results).Error; err != nil {
				return err
			}

			return nil
		})

	if batchesResult.Error != nil {
		return result.Error(batchesResult.Error, int64(0))
	} else {
		return result.Success(batchesResult.RowsAffected)
	}
}

func RemoveItemFromDistribution(r *shared.AppContext, name string, contentID uuid.UUID) result.Result[int64] {
	var results []olm.List
	now := r.AppTime().NowUTC()

	batchesResult := r.TenantDatabase().Model(&olm.List{}).Where("distributed and name = ?", name).
		FindInBatches(&results, 100, func(tx *gorm.DB, batch int) error {

			for i, _ := range results {
				results[i].Items = slicexx.Filter(results[i].Items, func(item olm.Item) bool {
					return item.ContentID != contentID
				})
				results[i].Track(now, r.Account().ID)
			}

			if err := tx.Save(&results).Error; err != nil {
				return err
			}

			return nil
		})

	if batchesResult.Error != nil {
		return result.Error(batchesResult.Error, int64(0))
	} else {
		return result.Success(batchesResult.RowsAffected)
	}
}

type CountResult struct {
	Name          string
	Count         int
	Total         int
	CurrentListID *uuid.UUID
	List          *olm.List `gorm:"-"`
}

func GetNumberOfDistributedListsWithContentID(r *shared.AppContext, contentID uuid.UUID, contentType string) result.Result[[]CountResult] {
	results := []CountResult{}
	if err := r.TenantDatabase().Raw(`
		WITH item_check AS (
		  SELECT
			name,
			CASE
			  WHEN items IS NULL THEN FALSE
			  WHEN jsonb_typeof(items) != 'array' THEN FALSE
			  ELSE EXISTS (
				SELECT 1
				FROM jsonb_array_elements(items) AS item
				WHERE (item->>'ContentID') = ?
			  )
			END AS contains_item,
		CASE
		  WHEN ? = ANY(sites) THEN id
		  ELSE NULL
		END as list_ids
		  FROM public.lists
		  WHERE active and distributed  and ? = ANY(content_types)
		)
		SELECT
		  name,
		  COUNT(*) FILTER (WHERE contains_item) AS count,
          COUNT(*) AS total,
          (array_remove(array_agg(list_ids), NULL))[1] AS current_list_id
		FROM item_check
		GROUP BY name
		ORDER BY name;
		`,
		contentID.String(), r.CurrentSiteID().String(), contentType).Scan(&results).Error; err != nil {
		return result.Error(err, results)
	}

	lists := []olm.List{}
	if err := r.TenantDatabase().Select("name, structure_id, override_sections, content_types").
		Group("name, structure_id, override_sections, content_types").
		Where("distributed and ? = ANY(content_types)", contentType).Preload("Structure").Find(&lists).Error; err != nil {
		return result.Error(err, results)
	}
	listsMap := slicexx.AsMap(lists, func(list olm.List) string {
		return list.Name
	})

	for i, _ := range results {
		if _, ok := listsMap[results[i].Name]; ok {
			results[i].List = converters.AsPointer(listsMap[results[i].Name])
		}
	}

	return result.Success(results)
}
