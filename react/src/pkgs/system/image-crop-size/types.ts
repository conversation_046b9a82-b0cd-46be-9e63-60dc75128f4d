import { paged, trackable } from '@/common/react-query'
import { z } from 'zod'

const imageCropSize = trackable.extend({
    ID: z.string(),
    Name: z.string(),
    Width: z.number(),
    Height: z.number(),
    Active: z.boolean()
})

export const imageCropSizes = paged.extend({
    Rows: z.array(imageCropSize)
})

export type ImageCropSize = z.infer<typeof imageCropSize>

const imageCropSizeDto = z.object({
    Name: z.string(),
    Width: z.number(),
    Height: z.number()
})

export type ImageCropSizeDTO = z.infer<typeof imageCropSizeDto>
