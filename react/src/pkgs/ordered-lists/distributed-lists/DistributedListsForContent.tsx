import CenteredSpinner from '@/common/components/CenteredSpinner'
import { Alert } from '@mui/material'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { List as ListDTO } from '@/pkgs/ordered-lists/types'
import { useState } from 'react'
import { ItemEditor } from '@/pkgs/ordered-lists/ItemEditor'
import { AddCircle, Circle } from '@mui/icons-material'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemAvatar from '@mui/material/ListItemAvatar'
import ListItemText from '@mui/material/ListItemText'
import IconButton from '@mui/material/IconButton'
import DeleteIcon from '@mui/icons-material/Delete'
import { useListItemsActions } from '@/pkgs/ordered-lists/distributed-lists/useListItemsActions'
import { colours } from '@/common/colours'

type DistributedListsForContentProps = {
    ContentID: string
    ContentType: string
}

export function DistributedListsForContent({ ContentID, ContentType }: DistributedListsForContentProps) {
    const {
        canUpdateDistributed,
        distributedQuery: query,
        deleteFromDistributed,
        addToDistribution,
        saving
    } = useListItemsActions(ContentID, ContentType)

    const [selectedList, setSelectedList] = useState<Pick<
        ListDTO,
        'Structure' | 'OverrideSections' | 'ContentTypes' | 'Name'
    > | null>(null)
    const [hoveredList, setHoveredList] = useState<string | null>(null)

    if (query.isLoading) {
        return <CenteredSpinner />
    }
    if (query.error) {
        return <Alert severity='error'>{guessErrorMessage(query.error)}</Alert>
    }

    if (!query.data || query.data.length === 0) {
        return <Alert severity='info'>No related lists found for {ContentType}</Alert>
    }

    return (
        <div>
            <List>
                {query.data?.map((listStats) => {
                    const color =
                        listStats.Count === listStats.Total ? 'success' : listStats.Count === 0 ? 'disabled' : 'warning'
                    const shortenedName =
                        listStats.Name.length > 30 ? `${listStats.Name.slice(0, 30)}...` : listStats.Name
                    const addTitle =
                        listStats.Count === 0 ? 'Add item to all lists' : 'Add item to the rest of the lists'
                    const circleTitle =
                        listStats.Count === 0
                            ? 'Item is not in this list'
                            : listStats.Count < listStats.Total
                              ? 'Item is in some lists'
                              : 'Item is in this list'
                    return (
                        <ListItem
                            sx={
                                canUpdateDistributed
                                    ? {
                                          backgroundColor:
                                              hoveredList === listStats.Name ? 'rgba(0, 0, 0, 0.04)' : 'inherit'
                                      }
                                    : {
                                          backgroundColor:
                                              hoveredList === listStats.Name
                                                  ? colours.disabled_row_hover
                                                  : colours.disabled_row
                                      }
                            }
                            onMouseEnter={() => {
                                setHoveredList(listStats.Name)
                            }}
                            onMouseLeave={() => {
                                setHoveredList(null)
                            }}
                            key={listStats.Name}
                            secondaryAction={
                                canUpdateDistributed &&
                                hoveredList === listStats.Name && (
                                    <>
                                        {listStats.Count > 0 && (
                                            <IconButton
                                                edge='end'
                                                aria-label='delete'
                                                title={'Remove item from all lists. '}
                                                disabled={saving}
                                                onClick={async () => {
                                                    if (
                                                        !confirm(
                                                            `Are you sure you want to remove this item from all ${listStats.Name} lists?`
                                                        )
                                                    ) {
                                                        return
                                                    }
                                                    await deleteFromDistributed(listStats.Name, ContentID)
                                                }}
                                            >
                                                <DeleteIcon color={'error'} />
                                            </IconButton>
                                        )}
                                        {listStats.Count < listStats.Total && (
                                            <IconButton
                                                edge='end'
                                                aria-label={addTitle}
                                                title={addTitle}
                                                sx={{ marginLeft: '10px' }}
                                                onClick={() => {
                                                    // @ts-ignore
                                                    setSelectedList(listStats.List)
                                                }}
                                            >
                                                <AddCircle color={'success'} />
                                            </IconButton>
                                        )}
                                    </>
                                )
                            }
                        >
                            <ListItemAvatar title={circleTitle}>
                                <Circle color={color} />
                            </ListItemAvatar>
                            <ListItemText
                                title={listStats.Name}
                                primary={shortenedName}
                                secondary={`${listStats.Count} / ${listStats.Total}`}
                            />
                        </ListItem>
                    )
                })}
            </List>
            {selectedList && (
                <ItemEditor
                    item={{ ContentID }}
                    saving={saving}
                    onSave={async (item) => {
                        addToDistribution(selectedList.Name, item)
                        setSelectedList(null)
                    }}
                    onCancel={() => setSelectedList(null)}
                    list={selectedList}
                />
            )}
        </div>
    )
}
