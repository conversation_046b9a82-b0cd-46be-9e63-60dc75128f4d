-- Deploy cm_tenant_db_DeploySchema:2025-03-31-Document-title to pg

BEGIN;
ALTER TABLE document DISABLE TRIGGER ALL;

ALTER TABLE document ADD COLUMN IF NOT EXISTS title text; -- null for Folders
ALTER TABLE document_history ADD COLUMN IF NOT EXISTS title text; -- null for Folders

-- Update the 'title' column based on 'filename'
UPDATE document
SET filename = REPLACE(filename, '#', '_'),
    title = REGEXP_REPLACE(
    -- Step 4: Remove consecutive spaces
        REGEXP_REPLACE(
            -- Step 3: Replace hash symbols with spaces
                REGEXP_REPLACE(
                    -- Step 2: Replace year ranges (e.g., 2015-2016 or 22-23) with a placeholder
                        REGEXP_REPLACE(
                                REPLACE(
                                        REGEXP_REPLACE(
                                            -- Step 1: Replace dashes with spaces
                                                filename,
                                                '(\d{2}|\d{4})-(\d{2}|\d{4})',  -- Match year ranges
                                                '\1##YEARRANGE##\2',            -- Replace with a placeholder
                                                'g'
                                        ),
                                        '-', ' '  -- Replace remaining dashes with spaces
                                ),
                                '##YEARRANGE##', '-', 'g'  -- Replace the placeholder with a dash
                        ),
                        '#', ' ', 'g'  -- Replace hash symbols with spaces
                ),
                ' {2,}', ' ', 'g'  -- Remove consecutive spaces
        ),
    -- Step 5: Remove file extension
        '\.[^.]+$',
        ''
            )
WHERE filename IS NOT NULL;  -- Only update rows where 'filename' is not NULL

ALTER TABLE document ENABLE TRIGGER ALL;

COMMIT;
