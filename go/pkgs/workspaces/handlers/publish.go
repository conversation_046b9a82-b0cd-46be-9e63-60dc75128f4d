package handlers

import (
	shared2 "contentmanager/pkgs/workspaces/shared"
	"fmt"
	"gorm.io/gorm"
)

// PublishWorkspacedEntity persists changes to DB. It copies any non-live entity to live workspace and updates `effective_in`.
// The source entity is deleted from DB, entity is mutated to `live`
// The function doesn't handle `Tracking`
func PublishWorkspacedEntity[T WorkspacedEntity](tx *gorm.DB, entity T) error {
	if entity.GetWorkspace() == "live" {
		return fmt.Errorf("Cannot publish live entity. ")
	}

	return tx.Transaction(func(tx *gorm.DB) error {
		var tenantWorkspaces []string
		if err := tx.Table("workspaces").Select("id").Where("active").Pluck("id", &tenantWorkspaces).Error; err != nil {
			return err
		}
		// Delete entity from workspace
		if err := tx.Table(entity.TableName()).Where("id = ? AND workspace = ?", entity.GetEntityID(), entity.GetWorkspace()).Delete(nil).Error; err != nil {
			return err
		}

		// Update live effective_in
		var entityWorkspaces []string
		if err := tx.Table(entity.TableName()).Select("workspace").Where("id = ?", entity.GetEntityID()).Pluck("workspace", &entityWorkspaces).Error; err != nil {
			return err
		}
		liveEffectiveIn := shared2.EffectiveIn("live", tenantWorkspaces, entityWorkspaces)

		entity.SetEffectiveIn(liveEffectiveIn)
		entity.SetWorkspace("live")

		if err := tx.Save(entity).Error; err != nil {
			return err
		}

		return nil
	})
}
