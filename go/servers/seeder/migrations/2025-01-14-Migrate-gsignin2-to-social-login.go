package migrations

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/service_context"
	seeder_content "contentmanager/servers/seeder/content"
	"github.com/PuerkitoBio/goquery"
	"gorm.io/gorm"
)

func MigrateGSignin2ToSocialLogin(sc service_context.ServiceContext) {
	seeder_content.UpdateContentForAllTenants(sc, getContent, updateTemplateContent)
}
func updateTemplateContent(document *goquery.Document) *goquery.Document {
	document.Find("#g-signin2").Each(func(_ int, ss *goquery.Selection) {
		ss.ReplaceWithHtml("<a href=\"/sys/auth/google/login\">Sign-in with Google</a>")
	})
	return document
}
func getContent(db *gorm.DB) []commonModels.Content {
	var content = []commonModels.Content{}
	if err := db.Where("content ILIKE '%g-signin2%'").Where("active").Where("type = 'template'").Find(&content).Error; err != nil {
		panic(err)
	}
	return content
}
