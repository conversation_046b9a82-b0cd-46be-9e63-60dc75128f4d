import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Switch, <PERSON><PERSON><PERSON>, Typo<PERSON> } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { Reservable } from '@/pkgs/reservation/types'
import { useAppContext } from '@/pkgs/auth/atoms'
import axios from 'axios'
import { ConfirmAction } from '@/common/components'
import { extend, isFutureTimestamp, Minute, Second, utc } from '@/pkgs/reservation/time'
import { Check, MoreVert } from '@mui/icons-material'
import { CustomMenu } from '@/common/components/custom-context-menu/CustomMenu'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { Moment } from 'moment-timezone'
import moment from 'moment'
import { useIDToName } from '@/pkgs/grid/cells/GridCells'
import { primaryTheme } from '@/app/theme'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import {
    isUserTheCurrentEditorForSession,
    isMatching<PERSON>ey,
    isExtendedLocked,
    isSessionLocked
} from '@/pkgs/reservation/reservable'
import {
    endEditingSession,
    endExtendedLock,
    isReservableAPIPath,
    overrideExtendedLock,
    updateEditingSession
} from '@/pkgs/reservation/requests'

const EditingSessionDuration = 10 * Minute

const interceptorMap: Record<string, { response: number; request: number }> = {}

interface ReservationSwitchProps {
    ID: string
    Workspace: string
    table: 'content' /* | future support */
    onChange?: (checked: boolean) => void
    disabled?: boolean
    hideExtendedLock?: boolean
}

export function ReservationSwitch({
    ID,
    Workspace,
    table,
    disabled,
    onChange,
    hideExtendedLock = false
}: ReservationSwitchProps) {
    const appContext = useAppContext()

    const userID = appContext.identity()?.ID
    const [entity, setEntity] = useState<Reservable | null>(null)
    const [editingSession, setEditingSession] = useState<string | null>(null)
    const [isEditMode, setIsEditMode] = useState<boolean>(false)
    const [isInactivityWindowOpen, setIsInactivityWindowOpen] = useState(false)

    const path = `/api/v2/content/${ID}/${Workspace}`

    useEffect(() => {
        interceptorMap[`${ID}-${Workspace}`] = {
            request: -1,
            response: -1
        }
        onSwitch(true)
        return () => {
            /** Interceptors are required for clearReservation to be successful.
             *  this scope doesn't seem to receive the updated state parameter.
             *  e.g if we want to have it such that clearReservation doesn't rely on interceptor, we'd do:
             *
             *    clearReservation(ID, table, editingSession)
             *
             *  But when this is instantiated, EditingSession is null, and when the clean-up occurs, it seems to snapshot that value.
             *  EditingSession can be a valid value when the clean-up is called, but it wont be reflected here.
             *  We can't call this cleanup function in a useEffect with editingSession dependency because then it'll constantly clearReservation while it's in use.
             * */
            endEditingSession(ID, Workspace, table).finally(() => {
                axios.interceptors.request.eject(interceptorMap[`${ID}-${Workspace}`].request)
                axios.interceptors.response.eject(interceptorMap[`${ID}-${Workspace}`].response)
            })
        }
    }, [])

    let promptUserTimeout: NodeJS.Timeout | undefined
    let endSessionTimeout: NodeJS.Timeout | undefined
    useEffect(() => {
        if (editingSession == null) {
            return
        }
        initReservationInterceptor(editingSession)
        const durationRemaining = new Date(editingSession).getTime() - new Date().getTime()

        // Prompt user to finish or extend their editing session one minute before the expiration time.
        promptUserTimeout = setTimeout(() => setIsInactivityWindowOpen(true), durationRemaining - Minute)
        // Finish clearing the session a few seconds before the time runs out.
        endSessionTimeout = setTimeout(() => onSwitch(false), durationRemaining - Second * 5)
        return () => {
            clearTimeout(promptUserTimeout)
            clearTimeout(endSessionTimeout)
        }
    }, [editingSession])

    useEffect(() => {
        const onBeforeUnload = async (ev: Event) => {
            ev.preventDefault()
            await endEditingSession(ID, Workspace, table)
            /**
             * TODO: Dialog shouldn't appear here:
             *  true/false/null should open a dialog to confirm you want to leave, but undefined is also causing the dialog to appear.
             * */
            return undefined
        }
        window.addEventListener('beforeunload', onBeforeUnload)
        return () => {
            window.removeEventListener('beforeunload', onBeforeUnload)
        }
    }, [ID, Workspace, editingSession])

    function initReservationInterceptor(resKey: string | null) {
        if (!interceptorMap[`${ID}-${Workspace}`]) {
            interceptorMap[`${ID}-${Workspace}`] = {
                request: -1,
                response: -1
            }
        }

        if (interceptorMap[`${ID}-${Workspace}`].request > 0) {
            axios.interceptors.request.eject(interceptorMap[`${ID}-${Workspace}`].request)
        }
        if (interceptorMap[`${ID}-${Workspace}`].response > 0) {
            axios.interceptors.response.eject(interceptorMap[`${ID}-${Workspace}`].response)
        }

        let nextEditingSession: string | null = null

        interceptorMap[`${ID}-${Workspace}`].request = axios.interceptors.request.use(
            function (config) {
                config.params = config.params || {}
                if (config.url !== path && !isReservableAPIPath(config.url, ID, Workspace)) {
                    return config
                }
                nextEditingSession = nextEditingSession || extend(EditingSessionDuration, resKey)
                config.params['EditingSession'] = resKey
                config.params['NextEditingSession'] = nextEditingSession
                return config
            },
            function (error) {
                return Promise.reject(error)
            }
        )

        interceptorMap[`${ID}-${Workspace}`].response = axios.interceptors.response.use(
            function (res) {
                if (res?.config?.url !== path || res.config.method != 'get') {
                    return res
                }
                const data = res.data as Reservable
                if (data?.EditingSession == null) {
                    handleSetEditingSession(null)
                    nextEditingSession = null
                    setIsEditMode(false)
                    return res
                }
                if (isMatchingKey(data.EditingSession, nextEditingSession)) {
                    handleSetEditingSession(data.EditingSession)
                    nextEditingSession = null
                    setIsEditMode(true)
                }
                return res
            },
            function (error) {
                return Promise.reject(error)
            }
        )
    }

    async function onSwitch(checked: boolean) {
        try {
            if (checked) {
                handleUpdateEditingSession()
            } else {
                handleEndEditingSession()
            }
        } catch (err) {
            handleCatch(err)
        } finally {
            setIsInactivityWindowOpen(false)
        }
    }

    async function handleUpdateEditingSession() {
        try {
            const nextUsedUntil = extend(EditingSessionDuration)
            const response = await updateEditingSession({
                ID: ID,
                Workspace: Workspace,
                Table: table,
                EditingSession: editingSession,
                NextEditingSession: nextUsedUntil,
                ExtendedLock: extendedLock?.toISOString()
            })
            handleChange(nextUsedUntil, response)
        } catch (err) {
            handleCatch(err)
        }
    }

    async function handleEndEditingSession() {
        try {
            if (editingSession == null) {
                // Possibility of the state value for EditingSession being null but the interceptors having a different value in its closure.
                // it should be safe to reset the interceptors for this case.
                initReservationInterceptor(null)
            }
            await endEditingSession(ID, Workspace, table)
            handleChange(null, {
                ID: ID,
                Workspace: Workspace,
                EditingSession: null,
                CurrentEditor: entity?.ExtendedLock ? entity?.CurrentEditor : null,
                ExtendedLock: entity?.ExtendedLock
            })
        } catch (err) {
            handleCatch(err)
        }
    }

    function handleSetEditingSession(key: string | null | undefined) {
        if (key == null) {
            setEditingSession(null)
        } else {
            const nextKey = utc(key).toISOString()
            setEditingSession(nextKey)
        }
    }

    function handleChange(key: string | null, reservable: Reservable | null | undefined) {
        if (reservable == null) {
            handleSetEditingSession(null)
            return
        }
        setEntity(reservable)
        if (isUserTheCurrentEditorForSession(reservable, key, userID)) {
            handleSetEditingSession(reservable.EditingSession)
            onChange?.(true)
            setIsEditMode(true)
        } else {
            handleSetEditingSession(null)
            onChange?.(false)
            setIsEditMode(false)
        }
    }

    /*
     * Extended Editing
     * */
    const [anchor, setAnchor] = React.useState<HTMLElement | null>(null)
    const [extendedLock, setExtendedLock] = React.useState<Moment | string | null | any>(
        entity?.ExtendedLock ? moment(entity?.ExtendedLock) : null
    )

    async function handleEndExtendedLock() {
        try {
            const response = await endExtendedLock(ID, Workspace, table)
            handleChange(editingSession, response)
            setExtendedLock(null)
        } catch (err) {
            handleCatch(err)
        } finally {
            setAnchor(null)
        }
    }

    async function handleAdminOverrideExtendedLock() {
        try {
            const res = await overrideExtendedLock(ID, Workspace, table)
            // @ts-ignore
            handleChange(null, res)
            setExtendedLock(null)
        } catch (err) {
            handleCatch(err)
        } finally {
            setAnchor(null)
        }
    }

    function ConflictDetails() {
        const name = useIDToName({
            tableName: 'account',
            ID: entity?.CurrentEditor || ''
        })

        /*
         * with an extended lock, we want to show who possesses the extended lock & the time until it expires.
         * When there is no lock, and no reservation conflict, we don't need to show anything.
         * */
        function getDetails() {
            if (entity == null) return null
            const displayName = name?.split?.(',')?.shift()?.trim() || name
            const date = moment(entity?.ExtendedLock)
            const format = date.hour() == 0 && date.minute() == 0 ? 'YYYY-MM-DD' : 'YYYY-MM-DD LT'
            if (isExtendedLocked(entity)) {
                if (entity?.CurrentEditor == userID) {
                    return `You locked this page until ${date.format(format)}`
                } else {
                    return `This page is locked by ${displayName} until ${date.format(format)}`
                }
            }

            if (!isMatchingKey(entity?.EditingSession, editingSession)) {
                if (isEditMode) return null

                if (!isFutureTimestamp(entity?.EditingSession)) return null

                if (entity?.CurrentEditor == userID) {
                    return `You're currently working on this page in another tab`
                } else {
                    return `Currently being edited by ${displayName ? displayName : 'another user'}`
                }
            }
        }

        return (
            <Typography variant={'caption'} sx={isEditMode ? undefined : { color: primaryTheme.palette.warning.main }}>
                {getDetails()}
            </Typography>
        )
    }

    function handleCatch(error: any) {
        if (error?.response && error?.response.status === 409) {
            setEntity(error?.response?.data?.Data)
            notify('This page is currently being edited by another user', 'error')
        } else {
            logAndNotify(error)
        }
    }

    const isOverrideEnabled =
        appContext.identity()?.IsAdmin && isExtendedLocked(entity) && entity?.CurrentEditor !== userID

    return (
        <>
            <Box>
                <Stack direction='row' component='label' alignItems='center' justifyContent='center'>
                    <Typography>Read</Typography>
                    <Switch
                        checked={isEditMode}
                        onChange={(e, checked) => onSwitch(checked)}
                        // `Color` prop doesn't seem to support dynamic changes based on state/re-render.
                        style={{
                            color: isEditMode ? primaryTheme.palette.primary.main : primaryTheme.palette.warning.main
                        }}
                        // Only disabled if the content is extended locked by another user.
                        disabled={isExtendedLocked(entity) && entity?.CurrentEditor !== userID}
                    />
                    <Typography>Edit</Typography>
                    {!hideExtendedLock && (
                        <IconButton
                            size={'small'}
                            style={{ padding: 0 }}
                            onClick={(e) => setAnchor(e.currentTarget)}
                            disabled={entity?.CurrentEditor !== userID && !isOverrideEnabled}
                        >
                            <MoreVert />
                        </IconButton>
                    )}
                </Stack>
                <ConflictDetails />
            </Box>
            <>
                {/* Extended Editing Menu*/}
                <CustomMenu anchorElement={anchor} onClose={() => setAnchor(null)}>
                    <Box
                        sx={{
                            width: '20rem',
                            padding: '12px 12px 0 12px'
                        }}
                    >
                        <Typography>Extended Editing until</Typography>
                        <div className={'flex-row-align-center'}>
                            <DateTimePicker
                                disablePast={true}
                                value={extendedLock}
                                onChange={(e) => setExtendedLock(e)}
                                disabled={entity?.CurrentEditor !== userID && isExtendedLocked(entity)}
                            />
                            <div className={'flex-row-align-center'} style={{ minWidth: '2rem' }}>
                                <IconButton
                                    size={'small'}
                                    disabled={entity?.CurrentEditor !== userID && isExtendedLocked(entity)}
                                    onClick={() => {
                                        handleUpdateEditingSession().finally(() => setAnchor(null))
                                    }}
                                >
                                    <Check />
                                </IconButton>
                            </div>
                        </div>
                        <Typography variant={'caption'}>
                            Note: This will block other users from editing this page until the chosen date/time has been
                            reached.
                        </Typography>
                        <DialogActions
                            sx={{
                                justifyContent: 'space-between',
                                px: 0
                            }}
                        >
                            <Button
                                variant={'text'}
                                size={'small'}
                                onClick={handleEndExtendedLock}
                                disabled={entity?.CurrentEditor !== userID || !isExtendedLocked(entity)}
                            >
                                Clear
                            </Button>
                            {/* Override should only appear for Admins when the content is locked by a different user. */}
                            {isOverrideEnabled && (
                                <Tooltip
                                    title={
                                        isSessionLocked(entity, editingSession)
                                            ? 'An Extended Lock can only be overridden when the active editing session is completed'
                                            : 'Override & Remove the Extended Lock that was placed on this content'
                                    }
                                >
                                    <span>
                                        <Button
                                            variant={'text'}
                                            color={'error'}
                                            size={'small'}
                                            disabled={isSessionLocked(entity, editingSession)}
                                            onClick={() => handleAdminOverrideExtendedLock()}
                                        >
                                            Override
                                        </Button>
                                    </span>
                                </Tooltip>
                            )}
                        </DialogActions>
                    </Box>
                </CustomMenu>
            </>
            <>
                {/*Inactivity Window*/}
                <ConfirmAction
                    open={isInactivityWindowOpen}
                    handleAgree={() => onSwitch(true)}
                    handleClose={() => console.log('Must choose an option')}
                    handleDisagree={() => onSwitch(false)}
                    text={'Your editing session will soon expire, do you want to extend it?'}
                    title={'Still editing?'}
                    disagreeLabel={'End Session'}
                    agreeLabel={'Extend'}
                />
            </>
        </>
    )
}

function logAndNotify(err: unknown) {
    const message = guessErrorMessage(err)
    notify(message, 'error')
    console.error({ err, message })
}
