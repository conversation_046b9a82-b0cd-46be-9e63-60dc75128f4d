import React from 'react'
import { But<PERSON>, Card, CardContent, Collapse } from '@mui/material'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft'
import { makeStyles } from '@mui/styles'

export const CollapsibleCard = (props) => {
    const { isOpen, collapsedSize, orientation, children } = props
    const classes = useSliderStyles()
    return (
        <Collapse
            orientation={orientation || 'horizontal'}
            in={isOpen}
            collapsedSize={collapsedSize != null && typeof collapsedSize === 'number' ? collapsedSize : 60}
            classes={{ root: classes.collapseRootWithVisibleOverflow }}
        >
            <CardRefForwarder {...props}>{children}</CardRefForwarder>
        </Collapse>
    )
}

const CardRefForwarder = React.forwardRef((props, ref) => {
    const { isOpen, handleSlide, children, ...rest } = props
    const containerRef = React.useRef(null)
    const classes = useSliderStyles()
    return (
        <Card ref={ref} {...rest} className={classes.card}>
            <CardContent className={'row'}>
                <div style={{ height: '0' }}>
                    <span
                        style={{ top: containerRef?.current?.offsetTop, left: containerRef?.current?.offsetLeft }}
                        className={classes.slideToggleFacade}
                    />
                    <Button ref={containerRef} color={'inherit'} className={classes.slideToggle} onClick={handleSlide}>
                        {isOpen ? (
                            <KeyboardArrowRightIcon fontSize={'large'} />
                        ) : (
                            <KeyboardArrowLeftIcon fontSize={'large'} />
                        )}
                    </Button>
                </div>
                <div className='col-xs-12' style={{ paddingLeft: '2rem' }}>
                    {children}
                </div>
            </CardContent>
        </Card>
    )
})

const useSliderStyles = makeStyles((theme) => ({
    slideToggle: {
        position: 'relative',
        right: '2rem',
        top: '9rem',
        height: '12rem',
        backgroundColor: '#fff',
        borderRadius: '2rem'
    },
    collapseRootWithVisibleOverflow: {
        overflow: 'visible'
    },
    slideToggleFacade: {
        height: '12rem',
        borderRadius: '2rem',
        width: '4rem',
        backgroundColor: '#fff',
        position: 'absolute'
    },
    card: {
        overflow: 'visible',
        width: '30rem',
        borderRadius: '30px 0 0 30px'
    }
}))
