package search

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
)

type (
	ISearchDataAccess interface {
		GetSearchResults(dbCon *gorm.DB, params QueryParams) ([]commonModels.Content, error)
	}
	searchDataAccessInt struct{}
)

var (
	validTypes = map[string]bool{
		"page":     true,
		"event":    true,
		"news":     true,
		"document": true,
	}
)

func (si searchDataAccessInt) GetSearchResults(dbCon *gorm.DB, params QueryParams) ([]commonModels.Content, error) {
	dbQuery := dbCon
	var searchChain = make([]commonModels.Content, 0)

	// search everything by default
	if len(params.Types) == 0 {
		params.Types = []string{"page", "event", "news", "document"}
	} else {
		params.Types = slicexx.Filter[string](params.Types, func(typ string) bool {
			return validTypes[typ]
		})
	}

	paramsMap := params.ToParamMap()
	query := buildUnionQuery(params.Types)

	err := dbQuery.
		Raw(query, paramsMap).
		Scan(&searchChain).Error
	if len(searchChain) == 0 && err == nil {
		return searchChain, gorm.ErrRecordNotFound
	} else if err != nil {
		return searchChain, err
	}
	return searchChain, nil
}

func buildContentQuery(paramTypes []string) string {
	types := slicexx.Filter(paramTypes, func(p string) bool {
		return p != "document"
	})
	if len(types) == 0 {
		return ""
	}

	// type = 'page' OR type = 'news' OR type = 'event'
	typesSubquery := slicexx.Select(types, func(p string) string {
		return fmt.Sprintf("type = '%s'", p)
	})
	typesQuery := strings.Join(typesSubquery, " OR ")
	query := `
				select 
					id
					, title
					, updated
					, created
					, active
					, route
					, privacy_level
					, type::text
					, pagelayout::text	
					, ts_headline('english', content.content, websearch_to_tsquery('english', @SearchTerm),  'MaxFragments=3') as content
					, ts_rank_cd( array[0.01,0.01,0.01,1], tsvector_index_col, query) as rank 
				from content, websearch_to_tsquery('english', @SearchTerm) query 
				WHERE query @@ tsvector_index_col 
					AND (content.route IS NOT NULL OR content.route <> '' OR length(content.route) > 0)
					AND (%s) 
					AND @SiteID = ANY(content.sites) 
					AND ((privacy_level & @PrivacyLevel) = privacy_level OR privacy_level = 0) 
					AND content.active = true 
					AND (publish_at IS NOT NULL AND publish_at <= now()) AND (expire_at IS NULL OR expire_at >= now())
					AND NOT settings @> '{"ContentIndexingConfig": {"ExcludeFromIndex": true}}'
				order by rank desc LIMIT @Limit
`
	return fmt.Sprintf(query, typesQuery)
}

func buildDocumentQuery(paramTypes []string) string {
	if !slicexx.Contains(paramTypes, "document") {
		return ""
	}
	query := `
				select 
					id
					, filename as title
					, updated
					, created
					, active
					, '' as route
					, privacy_level
					, type::text
					, 'document' as pagelayout
					, '' as content	
					, ts_rank_cd(array[0.01,0.01,0.01,1], tsvector_index_col, query) as rank 
				from document, websearch_to_tsquery('english', @SearchTerm) query 
				WHERE query @@ tsvector_index_col 
					AND active = true 
					AND @SiteID = ANY(document.sites) 
					AND ((privacy_level & @PrivacyLevel) = privacy_level OR privacy_level = 0)
				order by rank desc LIMIT @Limit
`
	return query
}

func buildUnionQuery(types []string) string {
	contentQuery := buildContentQuery(types)
	documentQuery := buildDocumentQuery(types)

	if contentQuery != "" && documentQuery != "" {
		query := `WITH contents as (%s), 
			documents as (%s) 
			select * from contents 
			UNION 
			select * from documents 
			order by rank desc LIMIT @Limit`
		return fmt.Sprintf(query, contentQuery, documentQuery)
	} else if contentQuery != "" {
		return contentQuery
	} else if documentQuery != "" {
		return documentQuery
	}

	panic(errors.New("Can't be here - no query built. "))
}

func ISearchDataAccessAdapter() ISearchDataAccess {
	return &searchDataAccessInt{}
}
