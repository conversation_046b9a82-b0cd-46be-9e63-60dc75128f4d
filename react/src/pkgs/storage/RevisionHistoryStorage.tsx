import { <PERSON>, Button, Link, <PERSON>ack, Typography } from '@mui/material'
import { IDToNameCell } from '../grid/cells/GridCells'
import moment from 'moment'
import { useAppNavigation } from '../../app/useAppNavigation'
import { colours } from '../../common/colours'
import CenteredSpinner from '../../common/components/CenteredSpinner'
import { HistoryType, sanitizeSysPeriod, useHistoryQuery } from '../history/queries'
import { Content } from '@/pkgs/content/types'
import { ObjectAsJson } from '@/common/ObjectAsJson'
import React from 'react'
import { getPublishStatus, publishStatusColour } from '../content/editor/ContentEditorSaveBar'
import { Dot } from '@/common/components/Dot'
import { Image } from './queries'

interface RevisionHistoryStorageProps {
    image: Image
}

export function RevisionHistoryStorage({ image }: RevisionHistoryStorageProps) {
    const { data, isLoading } = useHistoryQuery(image.id, HistoryType.MEDIA)
    const [as<PERSON><PERSON>, setAsJson] = React.useState(undefined)
    const { getRelativePath } = useAppNavigation()
    const revisionHistoryExists = !!data?.Rows

    if (isLoading) {
        return <CenteredSpinner />
    }

    return (
        <Box maxHeight={'500px'} overflow={'auto'}>
            {revisionHistoryExists &&
                data.Rows.map((row, idx) => {
                    const publishStatus = getPublishStatus(row.PublishAt, row.ExpireAt)
                    const dotColour = publishStatusColour[publishStatus]

                    return (
                        <Box
                            key={row.SysPeriod}
                            sx={{
                                padding: '12px',
                                '&:hover': {
                                    backgroundColor: colours.off_white
                                }
                            }}
                        >
                            <Stack direction='row' spacing='8px' sx={{ alignItems: 'center' }}>
                                <Dot styles={{ backgroundColor: dotColour }} />
                                <Typography variant='subtitle1'>
                                    {moment(sanitizeSysPeriod(row.SysPeriod)).format('MM/DD/YYYY, h:mm a')}
                                </Typography>
                            </Stack>
                            {row?.Publisher && (
                                <Link
                                    href={getRelativePath(`/user-management/accounts/${row.Publisher}`)}
                                    target='_blank'
                                    sx={{
                                        textDecoration: 'none',
                                        '&:hover': {
                                            textDecoration: 'underline'
                                        }
                                    }}
                                >
                                    <IDToNameCell tableName={'account'} ID={row.Publisher} />
                                </Link>
                            )}
                        </Box>
                    )
                })}

            {asJson && <ObjectAsJson obj={asJson} title={`Settings column`} onClose={() => setAsJson(undefined)} />}
        </Box>
    )
}
