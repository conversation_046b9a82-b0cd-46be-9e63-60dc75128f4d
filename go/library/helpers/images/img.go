package images

import (
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"html"
	"net/url"
	"strings"
)

func init() {
	handlebars.RegisterHelper("img", ImgHelper)
	handlebars.RegisterPartial("img", "{{#img}}{{/img}}") //src = {{src}} //// alt = {{alt}}

	handlebars.RegisterHelper("imgSrc", imgSrcHelper)
	handlebars.RegisterPartial("imgSrc", "{{#imgSrc}}{{/imgSrc}}")
}

// ImgHelper is a handlebars helper that returns an HTML string with the image tag
// - src is required. Can be absolute or relative (if relative, it must start with a slash). Relative will be converted to absolute.
// everything else is optional and can be passed as a context:
// `id="id-random" width=100 crop=true class="some-class" style="border: 1px solid red;"`
// resizing attributes (with rz- prefix):
// - rz-width: required, if not provided default always will be 300
// - rz-height: optional, but required if rz-crop is provided
// - rz-crop: optional, if provided, rz-height is required. Available options are: 1,2,3,4,5
// - rz-q: quality, optional, default is 80
func ImgHelper(options *handlebars.Options) handlebars.SafeString {
	src, ctx, err := buildRzUrl(options)
	if err != nil {
		return reportError(err)
	}

	b := strings.Builder{}
	b.WriteString("<img src=\"")
	b.WriteString(html.EscapeString(src))
	b.WriteString("\" ")

	for k, v := range ctx {
		if k == "src" || strings.HasPrefix(k, "rz-") {
			continue
		}
		b.WriteString(fmt.Sprintf("%s=\"%s\" ", k, html.EscapeString(fmt.Sprintf("%v", v))))
	}
	b.WriteString("/> ")
	return handlebars.SafeString(b.String())
}

func imgSrcHelper(options *handlebars.Options) handlebars.SafeString {
	src, _, err := buildRzUrl(options)
	if err != nil {
		return handlebars.SafeString(fmt.Sprintf("/* <!-- Error: %v --> */", err))
	}

	return handlebars.SafeString(src)
}

func buildRzUrl(options *handlebars.Options) (string, map[string]interface{}, error) {
	log := options.DataFrame().GetLogger()
	r, rErr := options.DataFrame().GetHelpers().RequestContext()
	if rErr != nil {
		err := fmt.Errorf("[ImgHelper]: %v", rErr)
		log.Error().Err(err).Msg("ImgHelper")
		return "", nil, err
	}

	ctx, ok := options.Ctx().(map[string]interface{})
	if !ok {
		err := fmt.Errorf("[ImgHelper]: context is not a map[string]interface{}")
		log.Error().Interface("options.Ctx", options.Ctx()).Err(err).Msg("ImgHelper")
		return "", nil, err
	}
	src, err := guessSrc(r, ctx)
	if err != nil {
		return "", nil, err
	}

	b := strings.Builder{}
	b.WriteString("?")
	for k, v := range ctx {
		if strings.HasPrefix(k, "rz-") {
			b.WriteString(k[3:] + "=" + url.QueryEscape(fmt.Sprintf("%v", v)) + "&")
		}
	}

	cashKey := getCacheKey(src)

	b.WriteString("url=" + url.QueryEscape(src))
	return "https://rz.imagineeverything.com/" + cashKey + b.String(), ctx, nil
}

func guessSrc(r *shared.AppContext, ctx map[string]interface{}) (string, error) {
	srcInf, ok := ctx["src"]
	if !ok {
		return "", fmt.Errorf("[ImgHelper]: src is required")
	}

	host := r.Request().Host
	if strings.HasSuffix(host, ".localhost") {
		host = strings.TrimSuffix(host, ".localhost")
	}

	if strings.Contains(host, ".contentmanager.imagineeverything.") {
		host = r.CurrentSite().PrimaryDomain
	}

	var src string
	switch srcInf.(type) {
	case string:
		src = srcInf.(string)
		if strings.HasPrefix(src, "/") {
			src = "https://" + host + src
		} else if strings.HasPrefix(src, "http://") || strings.HasPrefix(src, "https://") {
			// do nothing
		} else if uuid.FromStringOrNil(src) != uuid.Nil {
			src = "https://" + host + "/images/" + src
		} else {
			return "", fmt.Errorf("[ImgHelper]: src must start with a slash or http:// or https://")
		}
	case uuid.UUID:
		src = "https://" + host + "/images/" + srcInf.(uuid.UUID).String()
	case *uuid.UUID:
		id, ok := srcInf.(*uuid.UUID)
		if !ok || id == nil || *id == uuid.Nil {
			return "", fmt.Errorf("[ImgHelper]: src is nil uuid.UUID")
		}
		src = "https://" + host + "/images/" + id.String()
	case commonModels.Media:
		src = "https://" + host + "/images/" + srcInf.(commonModels.Media).ID.String()
	default:
		return "", fmt.Errorf("[ImgHelper]: src is not a string, uuid.UUID or commonModels.Media")
	}

	return src, nil
}

func getCacheKey(src string) string {
	u, err := url.Parse(src)
	if err != nil {
		return uuid.NewV4().String()
	}

	segments := strings.Split(u.Path, "/")
	for i := len(segments) - 1; i >= 0; i-- {
		if len(segments[i]) > 0 {
			return url.QueryEscape(segments[i])
		}
	}
	return uuid.NewV4().String()
}

func reportError(err error) handlebars.SafeString {
	return handlebars.SafeString(fmt.Sprintf("<!-- Error: %v -->", err))
}
