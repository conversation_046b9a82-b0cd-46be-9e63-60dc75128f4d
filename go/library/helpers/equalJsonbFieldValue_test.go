package helpers

import (
	"contentmanager/library/templates/hbs_helpers"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/tenant/public/viewmodels"
	"encoding/json"
	"reflect"
	"testing"
)

var templatePass = "<h1>Title</h1><div class=\"container\">{{#equalJsonbFieldValue Dct \"header.spotlightTitle\" \"Test2\"}}Values Equal{{else}}Values Not Equal{{/equalJsonbFieldValue}}</div>"
var templateFail = "<h1>Title</h1><div class=\"container\">{{#equalJsonbFieldValue Dct \"header.spotlightTitle\" \"FalseValue\"}}Values Equal{{else}}Values Not Equal{{/equalJsonbFieldValue}}</div>"
var templateValueWithNull = "<h1>Title</h1><div class=\"container\">{{#equalJsonbFieldValue Dct \"header.spotlightTitle\" nil}}Values Equal{{else}}Values Not Equal{{/equalJsonbFieldValue}}</div>"
var templateNullWithNull = "<h1>Title</h1><div class=\"container\">{{#equalJsonbFieldValue Dct \"header.spotlightFail\" nil}}Values Equal{{else}}Values Not Equal{{/equalJsonbFieldValue}}</div>"

var jsonRawMessage = []byte{123, 34, 104, 101, 97, 100, 101, 114, 34, 58, 32, 123, 34, 116, 101, 115, 116, 34, 58, 32, 34, 116, 101, 115, 116, 34, 44, 32, 34, 115, 112, 111, 116, 108, 105, 103, 104, 116, 70, 97, 105, 108, 34, 58, 32, 110, 117, 108, 108, 44, 32, 34, 115, 112, 111, 116, 108, 105, 103, 104, 116, 73, 109, 97, 103, 101, 34, 58, 32, 34, 47, 105, 109, 97, 103, 101, 115, 47, 108, 105, 110, 117, 120, 46, 112, 110, 103, 34, 44, 32, 34, 115, 112, 111, 116, 108, 105, 103, 104, 116, 82, 111, 117, 116, 101, 34, 58, 32, 34, 47, 116, 101, 115, 116, 34, 44, 32, 34, 115, 112, 111, 116, 108, 105, 103, 104, 116, 84, 105, 116, 108, 101, 34, 58, 32, 34, 84, 101, 115, 116, 50, 34, 44, 32, 34, 115, 112, 111, 116, 108, 105, 103, 104, 116, 68, 101, 115, 99, 114, 105, 112, 116, 105, 111, 110, 34, 58, 32, 34, 84, 101, 115, 116, 50, 34, 125, 44, 32, 34, 118, 101, 114, 115, 101, 115, 34, 58, 32, 91, 123, 34, 105, 100, 34, 58, 32, 34, 101, 57, 48, 98, 52, 99, 48, 48, 45, 53, 53, 55, 100, 45, 52, 99, 54, 50, 45, 97, 55, 99, 102, 45, 98, 98, 102, 57, 51, 56, 100, 55, 52, 99, 50, 99, 34, 44, 32, 34, 118, 101, 114, 115, 101, 84, 105, 116, 108, 101, 34, 58, 32, 34, 84, 101, 115, 116, 49, 34, 44, 32, 34, 118, 101, 114, 115, 101, 67, 111, 108, 111, 117, 114, 34, 58, 32, 34, 98, 97, 99, 107, 45, 54, 34, 44, 32, 34, 118, 101, 114, 115, 101, 68, 101, 115, 99, 114, 105, 112, 116, 105, 111, 110, 34, 58, 32, 34, 84, 101, 115, 116, 49, 34, 125, 44, 32, 123, 34, 105, 100, 34, 58, 32, 34, 57, 99, 56, 99, 54, 52, 101, 50, 45, 101, 99, 49, 55, 45, 52, 97, 51, 48, 45, 97, 101, 98, 53, 45, 56, 48, 57, 102, 99, 101, 49, 54, 55, 53, 97, 48, 34, 44, 32, 34, 118, 101, 114, 115, 101, 84, 105, 116, 108, 101, 34, 58, 32, 34, 84, 101, 115, 116, 50, 34, 44, 32, 34, 118, 101, 114, 115, 101, 67, 111, 108, 111, 117, 114, 34, 58, 32, 34, 98, 97, 99, 107, 45, 50, 34, 44, 32, 34, 118, 101, 114, 115, 101, 68, 101, 115, 99, 114, 105, 112, 116, 105, 111, 110, 34, 58, 32, 34, 84, 101, 115, 116, 50, 34, 125, 93, 44, 32, 34, 115, 99, 114, 105, 112, 116, 117, 114, 101, 34, 58, 32, 91, 93, 125}

func Test_equalJsonbFieldValue(t *testing.T) {
	contentTemplatePass, _ := handlebars.Parse(templatePass)
	contentTemplateFail, _ := handlebars.Parse(templateFail)
	contentTemplateNull, _ := handlebars.Parse(templateValueWithNull)
	contentTemplateNullWithNull, _ := handlebars.Parse(templateNullWithNull)
	baseVM := &viewmodels.BaseContentPage{
		Account:           nil,
		Content:           publicModels.ContentForHandlebars{},
		Site:              tenancyModels.SiteViewModel{},
		News:              nil,
		Dct:               json.RawMessage(jsonRawMessage),
		NavigationContent: publicModels.ContentForHandlebars{},
		CurrentTitle:      "",
	}

	tests := []struct {
		name          string
		template      *handlebars.Template
		baseViewModel *viewmodels.BaseContentPage
		want          string
		wantErr       bool
	}{
		{
			name:          "Value Equals",
			template:      contentTemplatePass,
			baseViewModel: baseVM,
			want:          "<h1>Title</h1><div class=\"container\">Values Equal</div>",
			wantErr:       false,
		},
		{
			name:          "Value Not Equals",
			template:      contentTemplateFail,
			baseViewModel: baseVM,
			want:          "<h1>Title</h1><div class=\"container\">Values Not Equal</div>",
			wantErr:       false,
		},
		{
			name:          "Value With Null",
			template:      contentTemplateNull,
			baseViewModel: baseVM,
			want:          "<h1>Title</h1><div class=\"container\">Values Not Equal</div>",
			wantErr:       false,
		},
		{
			name:          "Null With Null",
			template:      contentTemplateNullWithNull,
			baseViewModel: baseVM,
			want:          "<h1>Title</h1><div class=\"container\">Values Equal</div>",
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.template.ExecWithHelpers(tt.baseViewModel, hbsHelpers.NewHbsHelpers(NewMockRequest("https://example.com/path?page=1&random=111")))
			if (err != nil) != tt.wantErr {
				t.Errorf("equalJsonbFieldValue error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("equalJsonbFieldValue = %v, want %v", got, tt.want)
			}
		})
	}

}
