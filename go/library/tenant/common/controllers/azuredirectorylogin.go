package commonControllers

import (
	"contentmanager/etc/conf"
	"contentmanager/library/binding"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/claims"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login"
	"contentmanager/pkgs/auth/login/azure"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"encoding/base64"
	uuid "github.com/satori/go.uuid"
	"log"
	"net/http"
	"net/url"
)

type (
	DirectoryLogin struct{}
)

func (dl DirectoryLogin) Authorize(w http.ResponseWriter, r *shared.AppContext, manager token.TokenManager[identity.PublicAccount]) {
	//Allow Anonymous
	var responseForm azure.ResponseForm

	if err := binding.MapFromMaps(&responseForm, r.Request().PostForm); err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("Error decoding Azure response")
		utils.WriteStatusJSON(w, http.StatusBadRequest, map[string]interface{}{"Error": "Error decoding Azure response", "ErrorMessage": err.Error(), "ErrorData": err})
		return
	}

	if requestForStateByteArray, errSessionSite := base64.RawURLEncoding.DecodeString(responseForm.State); errSessionSite == nil {
		requestForState := string(requestForStateByteArray)
		if requestForStateUrl, requestStateUrlError := url.ParseRequestURI("https://" + requestForState); requestStateUrlError == nil {
			if r.Request().Host != requestForStateUrl.Host {
				http.Redirect(w, r.Request(), "https://"+requestForStateUrl.Host+conf.RouterAuthorize, http.StatusTemporaryRedirect)
				return
			}
		}
	}

	host, err := login.GetStateURL(responseForm.State)
	if err != nil {
		utils.WriteStatusJSON(w, http.StatusBadRequest, map[string]interface{}{"message": "missing required state value", "error": err})
		return
	}

	jwtToken, err := azure.Authenticate(r.TenantDatabase(), responseForm, manager)
	if err != nil {
		logging.FromContext(r.Request().Context()).Err(err).Msg("error signing in with azure")
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusUnauthorized)
		return
	}
	claims.SetCookies(w, jwtToken, host)
	http.Redirect(w, r.Request(), "https://"+host, http.StatusFound)
}

func (dl DirectoryLogin) RedirectToAdminConsent(w http.ResponseWriter, r *shared.AppContext) {
	//Allow Anonymous
	//config := cache.ICacheAdapter().GetObject(conf.TenancyConfigCacheKey).(config.AppConfig)
	appConfig := config.GetAppConfig()
	http.Redirect(w, r.Request(),
		appConfig.MSOAuthBaseUrl+
			appConfig.MSOAuthOrgConsent+
			"?client_id="+appConfig.MSOAuthApplicationID+
			"&redirect_uri="+appConfig.MSOAuthOrgConsentRedirect+
			"&state="+uuid.NewV4().String()+
			"&scope="+appConfig.MSOAuthScope,
		http.StatusSeeOther)
}
func (dl DirectoryLogin) ProcessAdminConsent(w http.ResponseWriter, r *shared.AppContext) {
	//Allow Anonymous
	log.Println(r.Request().PostForm)
	resp := " " +
		"<div style='width:100%; height:100%; margin:auto; text-align:center'>" +
		"	<a href='https://azure.microsoft.com/en-us/' style='position:absolute;top:10%;text-decoration: none;font-size: 1.2rem;'> Proceed to Azure Portal </a>" +
		"</div>" +
		""
	w.Write([]byte(resp))
}
