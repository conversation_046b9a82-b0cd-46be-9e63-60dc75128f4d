package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/amzn"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/resources/admin"
	emailGenerators2 "contentmanager/pkgs/notifications/email_generators"
	"contentmanager/pkgs/search_v2/index"
	"gorm.io/gorm"
)

// onContentChanged Only process "live" workspace content. No handling for non-live workspaces as of now.
func onContentChanged(r *shared.AppContext, contentObj content.Content) {

	// NOTE: Only process "live" workspace content.
	if contentObj.Workspace != "live" {
		return
	}

	if err := processResources(r.TenantDatabase(), contentObj); err != nil {
		r.Logger().Error().Err(err).Str("content_id", contentObj.ID.String()).Msg("[onContentChanged] Error processing resources")
	}

	amzn.CreatePathInvalidations(r, contentObj)

	switch contentObj.Type {
	case "page", "news", "event":
		index.UpdateIndexForContentID(r, contentObj.ID)
	case "alert":
		updateNotifications(r, contentObj)
	}
}

func updateNotifications(r *shared.AppContext, contentObj content.Content) {
	settings := contentObj.GetSettingsMap()
	alert := emailGenerators2.AlertForIssue{
		ID:    contentObj.ID,
		Sites: contentObj.Sites,
	}

	if has, ok := settings["HasEmailNotification"].(bool); ok && has {
		// valid Alert, should parse everything
		alert.HasEmailNotification = true
		alert.Subject = settings["EmailSubject"].(string)
		alert.Description = settings["EmailBody"].(string)

		status := contentObj.PublishStatus(r.AppTime().NowUTC())
		if status == "draft" || status == "expired" || contentObj.Active == false {
			// user unpublished the content or it expired -- no need to send email
			alert.HasEmailNotification = false
		} else {
			alert.StartDate = *contentObj.PublishAt
		}
	} else {
		alert.HasEmailNotification = false
	}

	cnf := emailGenerators2.NewSiteConfigAccessor(r.TenancyDB())

	nsErr := emailGenerators2.CreateAlert(r, cnf, alert)
	if nsErr != nil {
		r.Logger().Error().Err(nsErr).Msg("[NOTIFICATIONS] Error generating Alert Notification Emails. ")
	}
}

var ContentTypesToProcess = map[string]struct{}{
	"page": {}, "news": {}, "event": {}, "alert": {}, "fragment": {}, "template": {}, "distributed_page": {},
}

func processResources(db *gorm.DB, c content.Content) error {
	if _, ok := ContentTypesToProcess[string(c.Type)]; !ok {
		return nil
	}

	params := admin.ExtractResourcesParams{
		EntityID:        c.ID,
		EntityType:      c.Type.String(),
		ResourceSources: c.GetResourceContainers(),
	}

	return admin.ProcessResources(db, params)
}
