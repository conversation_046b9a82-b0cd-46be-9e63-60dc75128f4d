package multitenancy

import (
	"contentmanager/etc/conf"
	sharedDatabase "contentmanager/library/shared/database"
	"context"
	"fmt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"os"
	"time"
)

var configs = map[string]DBConfig{
	// dedicated DB
	"cm_cbe": {IdleConnections: 10, OpenConnections: 50},

	// shared DB
	"cm_peel":                {IdleConnections: 7, OpenConnections: 30},
	"cm_cssd":                {IdleConnections: 3, OpenConnections: 10},
	"cm_ecsd":                {IdleConnections: 3, OpenConnections: 10},
	"cm_limestone":           {IdleConnections: 2, OpenConnections: 6},
	"cm_sd23":                {IdleConnections: 2, OpenConnections: 6},
	"cm_rcsd":                {IdleConnections: 2, OpenConnections: 6},
	"cm_sd22":                {IdleConnections: 2, OpenConnections: 6},
	"cm_blackgold":           {IdleConnections: 1, OpenConnections: 4},
	"cm_livingsky":           {IdleConnections: 1, OpenConnections: 4},
	"cm_sd91":                {IdleConnections: 1, OpenConnections: 4},
	"cm_prairierose":         {IdleConnections: 1, OpenConnections: 4},
	"cm_sd27":                {IdleConnections: 1, OpenConnections: 4},
	"cm_nwsd":                {IdleConnections: 1, OpenConnections: 4},
	"cm_mhpsd":               {IdleConnections: 1, OpenConnections: 4},
	"cm_redeemer":            {IdleConnections: 1, OpenConnections: 4},
	"cm_hscsd":               {IdleConnections: 1, OpenConnections: 3},
	"cm_fmcsd":               {IdleConnections: 1, OpenConnections: 3},
	"cm_grasslands":          {IdleConnections: 1, OpenConnections: 3},
	"cm_hpsd":                {IdleConnections: 1, OpenConnections: 2},
	"cm_ctt":                 {IdleConnections: 1, OpenConnections: 2},
	"cm_hfcrd":               {IdleConnections: 1, OpenConnections: 2},
	"cm_ecacs":               {IdleConnections: 1, OpenConnections: 2},
	"cm_imagineeverything":   {IdleConnections: 1, OpenConnections: 2},
	"cm_yellowknifecatholic": {IdleConnections: 1, OpenConnections: 2},
}

type (
	DBParams struct {
		Context  context.Context
		Host     string
		Port     string
		Server   string
		User     string
		Password string
	}

	DBConfig struct {
		IdleConnections int
		OpenConnections int
	}
)

func CreateDBConnection(params DBParams) (*gorm.DB, error) {
	connectionString := fmt.Sprintf("host=%s port=%s dbname=%s user=%s password=%s sslmode=disable",
		params.Host, params.Port, params.Server, params.User, params.Password)

	val := os.Getenv("DEBUG")
	debug := val == "1" || val == "true"

	db, err := gorm.Open(postgres.Open(connectionString), createGormConfig(debug))
	if err != nil {
		return nil, fmt.Errorf("Fatal error connecting to database \n error: [%s] \n connection: [%s]", err, connectionString)
	}
	sqlDb, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("Fatal error connecting to database \n error: [%s] \n connection: [%s]", err, connectionString)
	}

	if dbConf, ok := configs[params.Server]; ok {
		sqlDb.SetMaxIdleConns(dbConf.IdleConnections)
		sqlDb.SetMaxOpenConns(dbConf.OpenConnections)
	} else {
		sqlDb.SetMaxIdleConns(conf.DatabaseMaxIdleConnections)
		sqlDb.SetMaxOpenConns(conf.DatabaseMaxOpenConnections)
	}
	sqlDb.SetConnMaxLifetime(conf.DatabaseMaxConnectionLifetime)

	ctx := context.WithValue(params.Context, "db:host", params.Host)

	//if err := db.Use(gormxx.ReservationPlugin{}); err != nil {
	//	return nil, err
	//}

	return db.WithContext(ctx), nil
}

func createGormConfig(debug bool) *gorm.Config {
	var ilogger logger.Interface
	if debug {
		logConfig := logger.Config{
			SlowThreshold:             time.Millisecond * 500,
			IgnoreRecordNotFoundError: false,
			LogLevel:                  logger.Info,
		}
		ilogger = sharedDatabase.New(logConfig)
	} else {
		logConfig := logger.Config{
			SlowThreshold:             time.Millisecond * 1000,
			IgnoreRecordNotFoundError: false,
			LogLevel:                  logger.Warn,
		}
		ilogger = sharedDatabase.New(logConfig)
	}

	return &gorm.Config{
		Logger: ilogger,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // use singular table name, table for `User` would be `user` with this option enabled
		},
	}
}
