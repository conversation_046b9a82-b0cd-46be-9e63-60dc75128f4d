package htmlxx

import (
	"bytes"
	"github.com/PuerkitoBio/goquery"
	"golang.org/x/net/html"
	"net/url"
	"regexp"
	"strings"
)

func ExtractTextFromHTMLForIndexingOrEmpty(html string) string {
	text, err := ExtractTextFromHTMLForIndexing(html)
	if err != nil {
		return ""
	}
	return text
}

func ExtractTextFromHTMLForIndexing(html string) (string, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return "", err
	}

	doc.Find("head, script, style, [data-noindex]").Remove()

	doc.Find("img").Each(func(index int, element *goquery.Selection) {
		altText, exists := element.Attr("alt")
		if exists {
			element.ReplaceWithHtml(altText)
		} else {
			element.Remove()
		}
	})
	doc.Find("iframe").Each(func(_ int, element *goquery.Selection) {
		src, exists := element.Attr("src")
		if exists {
			element.ReplaceWithHtml(src)
		} else {
			element.Remove()
		}
	})

	text := ExtractTextFromNode(doc.Get(0))

	return BeautifyText(text), nil
}

func ExtractTextFromNode(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var sb strings.Builder

	// For block elements, add a space before and after
	if isBlockElement(n.Data) {
		sb.WriteString("\n")
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		sb.WriteString(ExtractTextFromNode(c))
	}

	if isBlockElement(n.Data) {
		sb.WriteString("\n")
	}

	return sb.String()
}

func isBlockElement(tagName string) bool {
	blockElements := map[string]bool{
		"p":          true,
		"h1":         true,
		"h2":         true,
		"h3":         true,
		"h4":         true,
		"h5":         true,
		"h6":         true,
		"div":        true,
		"section":    true,
		"article":    true,
		"nav":        true,
		"header":     true,
		"footer":     true,
		"aside":      true,
		"address":    true,
		"blockquote": true,
		"pre":        true,
		"ul":         true,
		"ol":         true,
		"li":         true,
		"table":      true,
		"tr":         true,
		"td":         true,
		"th":         true,
		"form":       true,
		"fieldset":   true,
		"legend":     true,
		"dl":         true,
		"dt":         true,
		"dd":         true,
		"hr":         true,
		"br":         true,
	}

	return blockElements[strings.ToLower(tagName)]
}

var reExtraSpaces = regexp.MustCompile(`\s{2,}`)

func BeautifyText(s string) string {
	lines := strings.Split(s, "\n")
	var processedLines []string

	for _, line := range lines {
		line = reExtraSpaces.ReplaceAllString(line, " ")
		line = strings.TrimSpace(line)

		// Skip the line if it's empty or contains only spaces/tabs
		if len(line) == 0 {
			continue
		}

		processedLines = append(processedLines, line)
	}

	return strings.Join(processedLines, "\n")
}

// AppendQueryParamsToURLs scans all <a href>, <link href> and <script src> in the input HTML
// and adds the given params to each URL (preserving existing query strings and fragments).
func AppendQueryParamsToURLs(htmlStr string, params map[string]string) string {
	if len(params) == 0 || len(htmlStr) == 0 {
		return htmlStr
	}

	root, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return htmlStr
	}

	doc := goquery.NewDocumentFromNode(root)

	// helper to update a given selection's attr
	update := func(sel *goquery.Selection, attrName string) {
		sel.Each(func(_ int, s *goquery.Selection) {
			orig, ok := s.Attr(attrName)
			if !ok || orig == "" {
				return
			}
			updated := appendQueryToURL(orig, params)
			s.SetAttr(attrName, updated)
		})
	}

	// 3) Find and update each element
	update(doc.Find("a[href]"), "href")
	update(doc.Find("link[href]"), "href")
	update(doc.Find("script[src]"), "src")

	// 4) Render the modified tree back to a string
	var buf bytes.Buffer
	if err := html.Render(&buf, root); err != nil {
		// on render error, fallback
		return htmlStr
	}
	return buf.String()
}

func appendQueryToURL(urlStr string, params map[string]string) string {
	// Skip modifying URLs with certain schemes
	if strings.HasPrefix(urlStr, "#") ||
		strings.HasPrefix(urlStr, "javascript:") ||
		strings.HasPrefix(urlStr, "data:") ||
		strings.HasPrefix(urlStr, "mailto:") ||
		strings.HasPrefix(urlStr, "tel:") {
		return urlStr
	}

	// Parse the URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return urlStr
	}

	// Append the query parameters only for http, https, or relative URLs
	if parsedURL.Scheme == "" || parsedURL.Scheme == "http" || parsedURL.Scheme == "https" {
		// Get existing query values
		query := parsedURL.Query()

		// Add new parameters
		for key, value := range params {
			query.Add(key, value) // Use Add to preserve existing values
		}

		// Update the URL's query string
		parsedURL.RawQuery = query.Encode()
	}

	return parsedURL.String()
}
