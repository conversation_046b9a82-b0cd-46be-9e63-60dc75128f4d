package azure_ad

import (
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/msgraph"
	"errors"
	"fmt"
	"log"
	"net/url"
)

type (
	GroupMembership struct {
		ID                string   `json:"id"`
		Members           []Member `json:"transitiveMembers"`
		HasFullMembership bool
		client            *msgraph.GraphClient
	}
)

func RefreshMembershipSync(groups []GroupMembership) []GroupMembership {
	for i := 0; i < len(groups); i++ {
		if err := groups[i].Refresh(); err != nil {
			// Audit Records? How should this be handled?
			log.Println("error refreshing membership for group: ", groups[i].ID)
		}
	}
	return groups
}

// Refresh
// Groups are queried with $expand=members parameter, which gives up to 20 members.
// If the group has 20 members, it's suspected that they have more than 20 and the group needs to be queried directly.
// Refresh either marks the group as having full membership and returns early, or queries the group to fill the Members array.
func (gm *GroupMembership) Refresh() error {
	if gm.HasFullMembership {
		return nil
	}
	if len(gm.Members) != 20 {
		gm.HasFullMembership = true
		return nil
	}
	var err error
	if gm.Members, err = gm.getMemberHandler().getAll(); err != nil {
		return err
	}
	gm.HasFullMembership = true
	return nil
}
func (gm *GroupMembership) getMemberHandler() *MemberHandler {
	params := url.Values{
		"$select": []string{"givenName,surname,mail,userPrincipalName"},
		"$top":    []string{"999"},
	}
	return &MemberHandler{
		Members: make([]Member, 0),
		Pager: msgraph.Pager{
			NextToken: fmt.Sprintf("/groups/%v/transitiveMembers/microsoft.graph.user?%s", gm.ID, params.Encode()),
			Client:    gm.client,
		},
	}
}

type (
	GroupMembershipHandler struct {
		CurrentPage []GroupMembership `json:"value"`
		msgraph.Pager
	}
)

func GetMatchingGroupMemberships(client *msgraph.GraphClient, externalIds []string) ([]GroupMembership, error) {
	handler := NewGroupMembershipHandler(client)
	mapExternalId := slicexx.ToMap(externalIds, func(externalId string) string {
		return externalId
	}, func(_ string) bool {
		return true
	})
	var acc []GroupMembership
	for handler.CanPaginate() {
		if err := handler.Next(); err != nil {
			if !errors.Is(msgraph.ErrNoNextToken, err) {
				return acc, err
			}
			break
		}
		for _, group := range handler.CurrentPage {
			if _, ok := mapExternalId[group.ID]; ok {
				acc = append(acc, group)
			}
		}
	}
	return acc, nil
}
func NewGroupMembershipHandler(client *msgraph.GraphClient) *GroupMembershipHandler {
	params := url.Values{
		// No fields are required from the Group object
		"$select": []string{"id"},
		// Expand (*max up to 20) members - Only select required fields.
		// If len(members) == 20, it means that we have to query that group directly, as it could have
		// more members than $expand can return.
		"$expand": []string{"transitiveMembers($select=givenName,surname,mail,userPrincipalName)"},
		// Consider if 999 is an appropriate value here.
		//The data set returned by /groups with expanded members can be quite large - however, it hasn"t been problematic on local testing.
		"$top": []string{"999"},
	}
	// Params should be automatically attached to `Next` link (odata next url)
	return &GroupMembershipHandler{
		CurrentPage: make([]GroupMembership, 0),
		Pager: msgraph.Pager{
			NextToken: "/groups" + "?" + params.Encode(),
			Client:    client,
		},
	}
}

func (gh *GroupMembershipHandler) GetAll() ([]GroupMembership, error) {
	var acc []GroupMembership
	for gh.CanPaginate() {
		if err := gh.Next(); err != nil {
			if !errors.Is(msgraph.ErrNoNextToken, err) {
				return acc, err
			}
			break
		}
		acc = append(acc, gh.CurrentPage...)
	}
	return acc, nil
}

// Next
// Page Size & selected fields are set by func (*GraphClient) GetGroupMembershipHandler
func (gh *GroupMembershipHandler) Next() error {
	err := gh.Pager.NextPage(&gh)
	if err != nil {
		gh.CurrentPage = make([]GroupMembership, 0)
		return err
	}
	for i := 0; i < len(gh.CurrentPage); i++ {
		gh.CurrentPage[i].client = gh.Client
	}
	return nil
}
