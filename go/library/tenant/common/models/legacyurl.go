package commonModels

import (
	uuid "github.com/satori/go.uuid"
)

type LegacyURL struct {
	ID            uuid.UUID     `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	ContentID     uuid.UUID     `json:"content_id" gorm:"column:content_id;type:uuid;"`
	Url           string        `json:"url" gorm:"column:url;type:character varying(256)"`
	UrlNorm       string        `json:"url_norm" gorm:"column:url_norm;type:character varying(256)"`
	Active        bool          `json:"active" gorm:"column:active;type:boolean;"`
	ContentStatus ContentStatus `json:"content_status" gorm:"foreignkey:ContentID;references:ID"`
}
type LegacyURLViewModel struct {
	Active bool
	URL    string
}

func (LegacyURL) TableName() string {
	return "legacy_url"
}
