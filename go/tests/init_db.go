package tests

import (
	"bufio"
	config "contentmanager/etc/conf"
	"contentmanager/library/cache"
	"contentmanager/library/shared/database"
	"fmt"
	"github.com/johanhugg/gnomock"
	"github.com/johanhugg/gnomock/preset/postgres"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	"strings"
)

type DBType string

const (
	Tenant       DBType = "database/src/tenant/Deploy"
	Multitenancy DBType = "database/src/multitenancy/Deploy"
)

func InitTenantDB() (*gorm.DB, func()) {
	return InitDB(Tenant)
}

func InitMultitenancyDB() (*gorm.DB, func()) {
	return InitDB(Multitenancy)
}

func InitDB(dbType DBType) (*gorm.DB, func()) {
	sqitch, err := GetSqitchDeployQueries(dbType)
	if err != nil {
		panic(err)
	}
	sql := createUserSQL() + sqitch
	pg := postgres.Preset(
		postgres.WithVersion("14.1-alpine"),
		postgres.WithUser("gnomock", "gnomick"),
		postgres.WithDatabase("mydb"),
		postgres.WithQueries(sql),
	)
	container, err := gnomock.Start(pg, gnomock.WithUseLocalImagesFirst())
	if err != nil {
		panic(err)
	}
	connStr := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s  dbname=%s sslmode=disable",
		container.Host, container.DefaultPort(),
		"gnomock", "gnomick", "mydb",
	)
	dispose := func() { _ = gnomock.Stop(container) }
	db, err := sharedDatabase.CreateDatabaseConnectionByString(connStr)
	if err != nil {
		dispose()
		panic(err)
	}

	if dbType == Multitenancy {
		cache.ICacheAdapter().CacheObject(config.TenancyDatabaseConnectionCacheKey, db, false)
	}

	return db, dispose
}

func GetSqitchDeployQueries(dbType DBType) (string, error) {
	// Start from the current working directory
	dir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to get current working directory: %w", err)
	}

	// Search for the sqitch.plan file
	sqitchPlanPath := ""
	for {
		testPath := filepath.Join(dir, string(dbType), "sqitch.plan")
		if _, err := os.Stat(testPath); err == nil {
			sqitchPlanPath = testPath
			break
		}
		parentDir := filepath.Dir(dir)
		if parentDir == dir {
			// We've reached the root directory
			return "", fmt.Errorf("sqitch.plan not found for dbType %s", dbType)
		}
		dir = parentDir
	}

	// Read and process the sqitch.plan file
	file, err := os.Open(sqitchPlanPath)
	if err != nil {
		return "", fmt.Errorf("failed to open sqitch.plan: %w", err)
	}
	defer file.Close()

	var sql strings.Builder
	scanner := bufio.NewScanner(file)
	deployDir := filepath.Join(filepath.Dir(sqitchPlanPath), "deploy")

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "%") {
			continue
		}
		name := strings.Split(line, " ")[0]
		sqlFilePath := filepath.Join(deployDir, name+".sql")

		b, err := os.ReadFile(sqlFilePath)
		if err != nil {
			return "", fmt.Errorf("failed to read SQL file %s: %w", sqlFilePath, err)
		}
		sql.Write(b)
	}

	if err := scanner.Err(); err != nil {
		return "", fmt.Errorf("error scanning sqitch.plan: %w", err)
	}

	return sql.String(), nil
}

func createUserSQL() string {
	return `
		do
		$$
		begin
		  if not exists (select * from pg_user where usename = 'contentmanager_application_user') then
			 create role contentmanager_application_user password 'poiasjdflakmsdfkamsdfpoiuasdf';
		  end if;
		end
		$$
		;
`
}

func GetDB(sql string) (*gorm.DB, func()) {
	pg := postgres.Preset(
		postgres.WithVersion("14.1-alpine"),
		postgres.WithUser("gnomock", "gnomick"),
		postgres.WithDatabase("mydb"),
		postgres.WithQueries(sql),
	)
	container, err := gnomock.Start(pg)
	if err != nil {
		panic(err)
	}
	connStr := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s  dbname=%s sslmode=disable",
		container.Host, container.DefaultPort(),
		"gnomock", "gnomick", "mydb",
	)
	dispose := func() { _ = gnomock.Stop(container) }
	db, err := sharedDatabase.CreateDatabaseConnectionByString(connStr)
	if err != nil {
		dispose()
		panic(err)
	}
	return db, dispose
}
