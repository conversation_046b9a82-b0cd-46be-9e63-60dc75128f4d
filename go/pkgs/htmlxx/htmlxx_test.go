package htmlxx

import (
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"testing"
)

func Test_ExtractText(t *testing.T) {
	htmlString := readFileFromTestData("test.html")
	textExpected := stripWhitespace(readFileFromTestData("test.txt"))

	txt, err := ExtractTextFromHTMLForIndexing(htmlString)
	if err != nil {
		t.Fatal(err)
	}
	text := stripWhitespace(txt)
	if text != textExpected {
		t.Fatalf("expected text: \n%s, \ngot: \n%s", textExpected, text)
	}
}

func readFileFromTestData(filename string) string {
	path := filepath.Join("testdata", filename)
	file, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	bb, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}
	return string(bb)
}

var pattern = regexp.MustCompile(`(?s)\s+`)

func stripWhitespace(s string) string {
	return strings.TrimSpace(pattern.ReplaceAllString(s, " "))
}
