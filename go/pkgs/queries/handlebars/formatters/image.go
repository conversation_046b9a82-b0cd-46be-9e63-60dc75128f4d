package formatters

import (
	"contentmanager/library/helpers/images"
	"contentmanager/library/templates/hbs_helpers/shared"
	handlebars2 "contentmanager/library/tenant/public/utils/handlebars"
	"reflect"
)

func image(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars2.Options) handlebars2.SafeString {
	ctx := map[string]interface{}{
		"src": value,
	}
	if val, ok := value.(map[string]interface{}); ok {
		ctx = val
	}

	// Merge config into ctx if it exists
	if config != nil {
		for k, v := range config {
			switch k {
			case "width", "height", "crop":
				ctx["rz-"+k] = v
			}
		}
	}
	options.PushCtx(reflect.ValueOf(ctx))
	defer options.PopCtx()

	return images.ImgHelper(options)
}
