import { httpDelete, httpPut } from '@/common/client'
import { Reservable, reservable, ReservableTable, ReservableUpdate } from '@/pkgs/reservation/types'

export const ReservableAPI = '/api/v1/reservable'
export const ReservableLockAPI = `${ReservableAPI}/lock`
export const ReservableLockOverrideAPI = `${ReservableLockAPI}/override`

export async function updateEditingSession(params: ReservableUpdate) {
    return await httpPut(ReservableAPI, params, reservable)
}

export async function endEditingSession(id: string, workspace: string, table: string): Promise<void> {
    return httpDelete(`${ReservableAPI}?table=${table}&id=${id}&workspace=${workspace}`)
}

export async function endExtendedLock(id: string, workspace: string, table: string) {
    return httpDelete(`${ReservableLockAPI}?table=${table}&id=${id}&workspace=${workspace}`, reservable)
}

export async function overrideExtendedLock(id: string, workspace: string, table: string) {
    return httpDelete(`${ReservableLockOverrideAPI}?table=${table}&id=${id}&workspace=${workspace}`, reservable)
}

export function isReservableAPIPath(url: string | undefined, entityID: string, workspace: string): boolean {
    if (!url) {
        return false
    }
    try {
        const U = new URL(url, window.location.origin)
        return (
            U.pathname.includes(ReservableAPI) &&
            U.searchParams.get('id') === entityID &&
            U.searchParams.get('workspace') === workspace
        )
    } catch {
        return false
    }
}
