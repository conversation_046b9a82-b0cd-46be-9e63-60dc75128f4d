## Content Manager Administrative Portal

 
- Clone Repo
- npm install
- run from IDE, or npm start in the command line. 
- navigate to localhost:3000


*note*
- Posts will NOT be successful without proxying (Lighttpd / Nginx) due to CORS policy
- Without the Golang API running in parallel, [Accounts, Sites, Content, Schools] will not be available


*furthermore* 

- Once route proxying has been configurated, the Webpack Devserver Websockets will 
not be able to provide hotloading without further proxying of the websocket connection


# Docs

Data Capture Tables can be created using JavaScript Object Notation, a number of components can be chosen to create comprehensive DCT forms.

*Acceptable components
- Checkbox
- Image
- Text
- Textarea
- Select

### Necessary Fields

```json
"name": "program",                            <----- name defines the output: result and can be accessed in Go Templates =i.e=> {{program.title}}
    "title": "Great Programs, Great Rewards", <----- title defines the header for the DCT form
    "allowMultiple" : true,                   <----- allowMultiple defines the output: whether or not the component is iterable, =i.e=> true = news, false = a header
    "components": [{                          <----- insert acceptable components, (allowMultiple = true will allow for multiple of the given component)
      "name": "title",                        <----- name defines the output: Given name "program", this component would be available at  = {{program.title}}
      "label": "Program Title",
      "key": true, 
      "type": "text",                         <----- Insert valid component type here 
      "required": true, 
      "validate": "",
      "maximumLength": ""
    }],
```


### Components with a single layer of depth

```json	
//Desired output of the DCT form
        
    Header: {
        headline: “exampleText”,
        headlineImage: “ExampleImageBinary”,
    }

// Necessary JSON to create the corresponding components

{
     "name": "header",
     "title": "Headline News Story",
     "allowMultiple": false,
     "components": [{
       "name": "headline",
       "label": "Headline",
       "key": true,
       "type": "text",
       "required": true,
       "validate": "",
       "maximumLength": ""
     },
       {
         "name": "headlineImage",
         "label": "Headline Image",
         "key": true,
         "type": "image",
         "required": true,
         "validate": "",
         "maximumLength": ""
       }
     ]
   },
```

 ##### Components with a multiple layers of depth, like news.

```json
// This would produce an array of components containing a title,
// a description, box emphasis, as well as an image

[{
    "name": "program",
    "title": "Great Programs, Great Rewards",
    "allowMultiple" : true,
    "components": [{
      "name": "title",
      "label": "Program Title",
      "key": true,
      "type": "text",
      "required": true,
      "validate": "",
      "maximumLength": ""
    },
      {
        "name": "description",
        "label": "Program Description",
        "key": true,
        "type": "textarea",
        "required": true,
        "validate": "",
        "maximumLength": ""
      },
      {
        "name": "emphasis",
        "label": "Box Emphasis",
        "key": true,
        "type": "select",
        "options": [
          {"text":"Emphasized", "value":"Value for Option 1"},
          {"text":"Regular", "value":"Value for Option 2"}
        ],
        "required": true,
        "validate": "",
        "maximumLength": ""
      },
      {
        "name": "image",
        "label": "Cover Image",
        "key": true,
        "type": "image",
        "required": true,
        "validate": "",
        "maximumLength": ""
      }
    ]
  }]
```

# Handlebar Helpers

*note* Handlebar helpers end with the name of their function proceeded by a /, 
```
- eg - Opening: {{#siteData}}  Closing: {{/siteData}}
```

### News
##### Syntax
```html
{{
  #newsRender           <--- Function Name
   News                 <--- Struct that the function will Access
   6                    <--- Number of news items for the helper to display (up to #)
   3                    <--- Number of news items per row
   ""                   <--- Opening tag, I.E - > <div>
   ""                   <--- Closing tag, I.E - > </div>
}} 


```
##### Example
```html
    {{#newsRender News 6 3 "" "" }} 
            <div class="col s12 m12 l4">
                <a href="{{ route }}">
                    <div class="card medium">
                        {{#each Media}}
                        <div class="card-image">
                            <img src="/images/{{ filename }}"/>
                        </div>
                        {{/each}}
                        <div class="card-content">
                            <h4 class="card-title">{{ title }}</h4>
                        </div>
                    </div>
                </a>
            </div>
                      
    {{/newsRender}}
````



### Program DCT Data
 ##### Syntax
 ```html
{{
  #renderJsonBFieldPath             <--- Function name
  DCT                               <--- Struct the helper is accessing
  "Program"                         <--- Field that the struct is accessing
}}
```




 ##### Example - Access complex JSONB data via the renderJsonBFieldPath helper
 ```html
    {{#renderJsonbFieldPath Dct "program" }} 
      {{ title }} 
      <img src="{{ image }}" width="30" height="50" /> 
      {{ description }} 
    {{/renderJsonbFieldPath}}
```





### Site Data
 ##### Access Single layer depth via the siteData helper
 
``` 
- eg - {{ Site.name}}
``` 

 ##### Access complex settings data via the 
 
 ```
- eg - {{#siteData Site "phone"}}{{/siteData}}
```

 ##### General Fields for site Settings
 - phone
 - address 
 - postal
 - province
 - email 
 - city
 

### Contact Form 
 ##### Render a Contact Form which will take a name, email, phone number and message, and route it to the site-email using Amazon SES

   the site email can be updated in the "site" section of the administrative panel, simply toggle the edit button to edit your site specific settings.

```html
{{renderContactForm}}{{/renderContactForm}}
```
