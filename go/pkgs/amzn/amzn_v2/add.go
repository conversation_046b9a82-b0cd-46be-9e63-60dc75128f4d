package amzn_v2

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/storage"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"strings"
	"time"
)

const (
	BUCKET_NAME                 = "cm-prod-v2"
	FOLDER_NAME                 = "files"
	MAX_FILE_SIZE int64         = 1024 * 1024 * 10 // 10MB
	EXPIRATION    time.Duration = 15 * time.Minute
)

func AddAmzn2(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/aws", func(router httpService.Router) {

		router.Get("/upload-policy", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery struct {
				FileName    string
				ContentType string
			}
		}) {
			key, err := generateKeyForImage(r.TenantID(), params.FromQuery.ContentType)
			if err != nil {
				utils.WriteError(w, err)
				return
			}

			res, err := GeneratePresignedPostPolicy(r.Request().Context(), PolicyProps{
				BucketName:  BUCKET_NAME,
				Key:         key,
				Expiration:  EXPIRATION,
				MaxFileSize: MAX_FILE_SIZE,
				ContentType: params.FromQuery.ContentType, // validated by `generateKeyForImage`
			})
			if err != nil {
				utils.WriteError(w, err)
				return
			}
			utils.WriteResponseJSON(w, res, nil)
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}

func generateKeyForImage(tenantID uuid.UUID, contentType string) (string, error) {
	ext, ok := storage.GetExtension(contentType)
	if !ok {
		return "", errors.New("invalid file type")
	}
	uniqueID := strings.ReplaceAll(uuid.NewV4().String(), "-", "")
	prefix1 := uniqueID[0:2] // First 2 characters
	prefix2 := uniqueID[2:4] // Next 2 characters
	rest := uniqueID[4:]     // Remaining characters
	return strings.Join([]string{tenantID.String(), FOLDER_NAME, prefix1, prefix2, rest + ext}, "/"), nil
}
