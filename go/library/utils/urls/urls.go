package urls

import (
	"net/url"
	"regexp"
	"strings"
)

var (
	noWWWnoTrailingSlash = regexp.MustCompile(`(?i)(?:^www\.|/+$)`)
	noTrailingSlash      = regexp.MustCompile(`(?i)/+$`)
	noPort               = regexp.MustCompile(`:[0-9]+$`)
)

// Normalize returns uppercase URL as a string with removed protocol, www suffix, port, trailing '/' and query
func Normalize(u *url.URL) string {
	if u.IsAbs() {
		full := noPort.ReplaceAllString(u.Host, "") + u.Path
		return strings.ToUpper(noWWWnoTrailingSlash.ReplaceAllString(full, ""))
	} else {
		return strings.ToUpper(noTrailingSlash.ReplaceAllString(u.Path, ""))
	}
}

// EqualNorms compares URLs ignoring www suffix, port, protocol and trailing /.
func EqualNorms(u1, u2 *url.URL) bool {
	return Normalize(u1) == Normalize(u2)
}

// EqualPathsNorms compares paths only ignoring trailing / (absolute paths != relative)
func EqualPathsNorms(u1, u2 *url.URL) bool {
	u1p, _ := url.Parse(u1.Path)
	u2p, _ := url.Parse(u2.Path)
	return EqualNorms(u1p, u2p)
}

func ReplaceGetParam(u *url.URL, name string, value string) *url.URL {
	newUrl := u
	currentValues := newUrl.Query()
	currentValues.Del(name)
	currentValues.Add(name, value)
	newUrl.RawQuery = currentValues.Encode()
	return newUrl
}

func GetSearchParam(URL string, param string) (string, bool) {
	v, err := url.Parse(URL)
	if err != nil {
		return "", false
	}
	values := v.Query()[param]
	if len(values) == 0 {
		return "", false
	}
	return values[0], true
}
