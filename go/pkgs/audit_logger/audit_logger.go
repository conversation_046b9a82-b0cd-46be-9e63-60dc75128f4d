package audit_logger

import (
	"contentmanager/logging"
	"contentmanager/logging/stacktrace"
	"contentmanager/pkgs/notifications/models"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

type (
	IAuditLogger interface {
		CantSendIssueToSubscriber(err error, issue models.Issue, subscriber models.Subscriber, relayID string)
		SendIssueStarted(issueID uuid.UUID, relayID string)
		SendIssueStopped(issueID uuid.UUID, relayID string, dur time.Duration)
		SendIssueStoppedError(err error, issueID uuid.UUID, relayID string, dur time.Duration)
	}

	auditLogger struct {
		db *gorm.DB
	}
)

func LogAudit(tenantDb *gorm.DB, record *AuditRecord) {
	go func() {
		if e := tenantDb.Create(&record).Error; e != nil {
			l := logging.RootLogger()
			l.Error().Err(e).Interface("record", record).Msg("Can't save audit record")
		}
	}()
}

func LogAuditEvent(tenantDB *gorm.DB, event AuditEvent, message string, inf ...interface{}) {
	go func() {
		record := &AuditRecord{
			CreatedAt: time.Now().UTC(),
			Event:     event,
			Message:   message,
			XData:     nil,
		}
		if len(inf) > 0 {
			if b, err := json.Marshal(inf); err == nil {
				record.XData = b
			} else {
				l := logging.RootLogger()
				l.Error().Err(err).Msg("Can't serialize objets")
			}
		}
		LogAudit(tenantDB, record)
	}()
}

func LogAuditInfo(tenantDB *gorm.DB, message string, inf ...interface{}) {
	go func() {
		record := &AuditRecord{
			CreatedAt: time.Now().UTC(),
			Event:     "info",
			Message:   message,
			XData:     nil,
		}
		if len(inf) > 0 {
			if b, err := json.Marshal(inf); err == nil {
				record.XData = b
			} else {
				l := logging.RootLogger()
				l.Error().Err(err).Msg("Can't serialize objets")
			}
		}
		LogAudit(tenantDB, record)
	}()
}

func LogAuditError(tenantDB *gorm.DB, message string, e error) {
	go func() {
		record := &AuditRecord{
			CreatedAt: time.Now().UTC(),
			Event:     "error",
			Message:   message,
			XData:     nil,
		}
		if b, err := json.Marshal(map[string]interface{}{
			"error": e.Error(),
			"stack": stacktrace.ErrorMarshaller(e),
		}); err == nil {
			record.XData = b
		} else {
			l := logging.RootLogger()
			l.Error().Err(err).Msgf("Can't serialize error %+v", e)
		}
		LogAudit(tenantDB, record)
	}()
}

func (a *auditLogger) CantSendIssueToSubscriber(err error, issue models.Issue, subscriber models.Subscriber, relayID string) {
	go func() {
		record := AuditRecord{
			SubscriberID: &subscriber.ID,
			IssueID:      &issue.ID,
			TopicID:      &issue.TopicID,
			CreatedAt:    time.Now().UTC(),
			Event:        CantSendIssueToSubscriber,
			Message:      fmt.Sprintf(`[%v]: Can't send issue to email with relay: %v -> %v, %s`, err, issue.ID, subscriber.Email, relayID),
			XData:        nil,
		}
		a.create(&record)
	}()
}

func (a *auditLogger) SendIssueStarted(issueID uuid.UUID, relayID string) {
	go func() {
		record := AuditRecord{
			IssueID:   &issueID,
			CreatedAt: time.Now().UTC(),
			Event:     SendIssueStarted,
			Message:   fmt.Sprintf(`Sending Started: issue with relay: %v <-> %s`, issueID, relayID),
			XData:     nil,
		}
		a.create(&record)
	}()
}

func (a *auditLogger) SendIssueStopped(issueID uuid.UUID, relayID string, dur time.Duration) {
	go func() {
		record := AuditRecord{
			IssueID:   &issueID,
			CreatedAt: time.Now().UTC(),
			Event:     SendIssueStopped,
			Message:   fmt.Sprintf(`Sending Stopped in %s: issue with relay: %v <-> %s`, a.roundDur(dur), issueID, relayID),
			XData:     nil,
		}
		a.create(&record)
	}()
}

func (a *auditLogger) SendIssueStoppedError(err error, issueID uuid.UUID, relayID string, dur time.Duration) {
	go func() {
		record := AuditRecord{
			IssueID:   &issueID,
			CreatedAt: time.Now().UTC(),
			Event:     SendIssueStoppedError,
			Message:   fmt.Sprintf(`[Error]: %v Sending Stopped in %s: issue with relay: %v <-> %s`, a.roundDur(dur), err, issueID, relayID),
			XData:     nil,
		}
		a.create(&record)
	}()
}

func (a *auditLogger) roundDur(d time.Duration) time.Duration {
	if d > 5*time.Hour {
		return d.Round(time.Hour)
	}
	if d > 5*time.Minute {
		return d.Round(time.Minute)
	}
	if d > 5*time.Second {
		return d.Round(time.Second)
	}
	return d
}

func (a *auditLogger) create(record *AuditRecord) {
	if e := a.db.Create(&record).Error; e != nil {
		l := logging.RootLogger()
		l.Error().Err(e).Interface("record", record).Msg("Can't save audit record")
	}
}

var _ IAuditLogger = (*auditLogger)(nil)

func NewAuditLogger(db *gorm.DB) IAuditLogger {
	return &auditLogger{
		db: db,
	}
}
