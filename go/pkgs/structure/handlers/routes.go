package handlers

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

func AddStructure(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/structure", func(router httpService.Router) {
		router.Get("", Structure_GET)
		router.Get("/:id", StructureById_GET)
		router.Post("", Structure_POST)
		router.Post("/clone", Structure_Clone)
		router.Put("/:id", Structure_PUT)
		router.Delete("/:id", Structure_DELETE)

		router.Post("/convert-from-dct", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, ConvertFromDCT(r))
		})

		router.Post("/migrate-dct-to-structure/:structureID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				StructureID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, MigratePagesToStructuredPages(r, params.FromPath.StructureID))
		})
		router.Post("/migrate-all-to-structure", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, MigrateAllContent(r))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

	return r
}
