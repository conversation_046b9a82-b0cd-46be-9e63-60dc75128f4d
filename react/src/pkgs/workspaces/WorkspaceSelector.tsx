import { BASE } from '@/common/constants'
import { Alert, FormControl, MenuItem, Select } from '@mui/material'
import { z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import { httpGet } from '@/common/client'
import { LoadingSpinner } from '@/common/components/CenteredSpinner'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useEffect, useState } from 'react'

type WorkspaceSelectorProps = {
    entityID: string
    tableName: string
    value: string
    onChange: (v: string) => void
    onDisabledWorkspacesChange?: (disabledWorkspaces: string[]) => void
}

export const WorkspaceSelector = ({
    value,
    onChange,
    entityID,
    tableName,
    onDisabledWorkspacesChange
}: WorkspaceSelectorProps) => {
    // TODO: @Anatoly get workspaces from the server (should be in AppContext)
    const [availableWorkspaces, setAvailableWorkspaces] = useState<string[]>(['live', 'draft'])
    const [disabledWorkspaces, setDisabledWorkspaces] = useState<string[]>([])

    // We can "clone" only live workspace (we may reconsider it later)
    const result = useQuery({
        queryKey: ['workspaces', entityID, tableName],
        enabled: !!entityID && !!tableName,
        refetchOnWindowFocus: false,
        queryFn: async () => {
            return httpGet(`${BASE}/api/v1/workspaces/${tableName}/${entityID}`, null, z.array(z.string()))
        }
    })

    useEffect(() => {
        if (!result.data) return

        const disabled = result.data.includes('live') ? [] : availableWorkspaces.filter((w) => !result.data.includes(w))
        setDisabledWorkspaces(disabled)
        onDisabledWorkspacesChange?.(disabled)
    }, [result.data, onDisabledWorkspacesChange])

    if (result.isLoading) {
        return <LoadingSpinner />
    }

    if (result.isError) {
        return <Alert severity='error'>Error loading workspaces: {guessErrorMessage(result.error)}</Alert>
    }

    return (
        <FormControl sx={{ width: '100px' }}>
            <Select
                size='small'
                value={value}
                onChange={(v) => {
                    onChange(v.target.value as string)
                }}
            >
                {availableWorkspaces.map((o) => (
                    <MenuItem key={o} value={o} disabled={disabledWorkspaces.includes(o)}>
                        {o}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
}
