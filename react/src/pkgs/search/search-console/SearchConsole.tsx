import { Box } from '@mui/system'
import { Tab<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bPanel } from '@mui/lab'
import { Alert, Checkbox, FormControlLabel, FormGroup, Grid, Switch, Tab, TextField, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { useStateWithStorage } from '@/common/storage.service'
import { useAppContext } from '@/pkgs/auth/atoms'
import { useDebounce } from '@/common/useDebounce'
import { SearchQuery, useSearchQuery } from '@/pkgs/search/types'
import { defaultPageQuery } from '@/common/react-query'
import { SearchResults } from '@/pkgs/search/search-console/SearchResults'
import { Pagination } from '@/common/components/Pagination'
import { SpellCheck } from '@/pkgs/search/search-console/SpellCheck'
import { PromotionsGrid } from '@/pkgs/search/promotions/PromotionsGrid'
import { MatchPromotions } from '@/pkgs/search/search-console/MatchPromotions'
import { SuggestionsGrid } from '@/pkgs/search/suggestions/SuggestionsGrid'
import { MatchSuggestions } from '@/pkgs/search/search-console/MatchSuggestions'

export function SearchConsole() {
    const appContext = useAppContext()
    const [currentTab, setCurrentTab] = useStateWithStorage('search-console-tab', 'main')

    const defaultQuery: SearchQuery = {
        ...defaultPageQuery,
        SearchText: '',
        Types: ['document', 'page', 'news', 'event'],
        IncludeSites: true,
        AllTags: true,
        PrivacyLevel: 0,
        IgnoreCurrentSite: false
    }
    const [query, setQuery] = useStateWithStorage('search-console-query-v2', defaultQuery)
    // const [query, setQuery] = useState(defaultQuery)
    const debouncedQuery = useDebounce(query, 1000)
    const results = useSearchQuery(debouncedQuery)

    useEffect(() => {
        results.refetch()
    }, [appContext.currentSiteID])

    const hasPermission = appContext.entityScopeAny('cm.full_text_search')

    return (
        <div>
            <h1>Search Console</h1>

            <TabContext value={currentTab}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <TabList onChange={(e, newValue) => setCurrentTab(newValue)}>
                        <Tab label={'Test Search'} value={'main'} sx={{ fontSize: '16px' }} />
                        {hasPermission && <Tab label={'Promotions'} value={'promotions'} sx={{ fontSize: '16px' }} />}
                        {hasPermission && <Tab label={'Suggestions'} value={'suggestions'} sx={{ fontSize: '16px' }} />}
                    </TabList>
                </Box>
                <TabPanel value={'main'}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <TextField
                            label={'Search content and documents'}
                            value={query.SearchText}
                            onChange={(e) => setQuery({ ...query, page: 1, SearchText: e.target.value })}
                            fullWidth
                        />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <FormGroup row>
                            <FormControlLabel
                                label={'document'}
                                control={
                                    <Checkbox
                                        checked={query.Types.includes('document')}
                                        onChange={(_, checked) => {
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                Types: checked
                                                    ? [...query.Types, 'document']
                                                    : query.Types.filter((t) => t !== 'document')
                                            })
                                        }}
                                    />
                                }
                            />
                            <FormControlLabel
                                label={'page'}
                                control={
                                    <Checkbox
                                        checked={query.Types.includes('page')}
                                        onChange={(_, checked) => {
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                Types: checked
                                                    ? [...query.Types, 'page']
                                                    : query.Types.filter((t) => t !== 'page')
                                            })
                                        }}
                                    />
                                }
                            />
                            <FormControlLabel
                                label={'news'}
                                control={
                                    <Checkbox
                                        checked={query.Types.includes('news')}
                                        onChange={(_, checked) => {
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                Types: checked
                                                    ? [...query.Types, 'news']
                                                    : query.Types.filter((t) => t !== 'news')
                                            })
                                        }}
                                    />
                                }
                            />
                            <FormControlLabel
                                label={'event'}
                                control={
                                    <Checkbox
                                        checked={query.Types.includes('event')}
                                        onChange={(_, checked) => {
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                Types: checked
                                                    ? [...query.Types, 'event']
                                                    : query.Types.filter((t) => t !== 'event')
                                            })
                                        }}
                                    />
                                }
                            />
                        </FormGroup>

                        <FormGroup row sx={{ marginLeft: 'auto' }}>
                            <FormControlLabel
                                label={'Only Public'}
                                labelPlacement={'start'}
                                control={
                                    <Switch
                                        checked={query.PrivacyLevel == 0}
                                        onChange={(_, checked) =>
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                PrivacyLevel: checked ? 0 : 2
                                            })
                                        }
                                    />
                                }
                            />
                        </FormGroup>

                        <FormGroup row>
                            <FormControlLabel
                                label={'Ignore Current Site'}
                                labelPlacement={'start'}
                                control={
                                    <Switch
                                        checked={!!query.IgnoreCurrentSite}
                                        onChange={(_, checked) =>
                                            setQuery({
                                                ...query,
                                                page: 1,
                                                IgnoreCurrentSite: checked
                                            })
                                        }
                                    />
                                }
                            />
                        </FormGroup>
                    </Box>

                    <Grid container spacing={2} sx={{ py: 2 }}>
                        <Grid item xs={8}>
                            <SpellCheck
                                value={debouncedQuery.SearchText}
                                onChange={(v) => setQuery({ ...query, SearchText: v, page: 1 })}
                            />

                            {results.data && (
                                <Pagination
                                    value={results.data}
                                    onPageChange={(page) => setQuery({ ...query, page })}
                                    onPageSizeChange={(pageSize) => setQuery({ ...query, page: 1, pageSize })}
                                />
                            )}

                            <SearchResults results={results} />
                        </Grid>
                        <Grid item xs={4}>
                            {!debouncedQuery.SearchText && (
                                <Alert severity='info'>
                                    <strong>Suggestions</strong> and <strong>Promotions</strong> are only available if
                                    search text is not empty.{' '}
                                </Alert>
                            )}

                            {debouncedQuery.SearchText && (
                                <>
                                    <Typography variant={'h6'} sx={{ p: 1, m: 1 }}>
                                        Suggestions
                                    </Typography>
                                    <MatchSuggestions
                                        query={{
                                            SearchText: debouncedQuery.SearchText,
                                            AllSites: debouncedQuery.IncludeSites
                                        }}
                                        onClick={(phrase) => setQuery({ ...query, SearchText: phrase })}
                                    />
                                </>
                            )}

                            {debouncedQuery.SearchText && (
                                <>
                                    <Typography variant={'h6'} sx={{ p: 1, m: 1 }}>
                                        Promotions
                                    </Typography>
                                    <MatchPromotions
                                        query={{
                                            SearchText: debouncedQuery.SearchText,
                                            AllSites: debouncedQuery.IncludeSites
                                        }}
                                    />
                                </>
                            )}
                        </Grid>
                    </Grid>
                </TabPanel>
                {hasPermission && (
                    <TabPanel value={'promotions'}>
                        <PromotionsGrid />
                    </TabPanel>
                )}
                {hasPermission && (
                    <TabPanel value={'suggestions'}>
                        <SuggestionsGrid />
                    </TabPanel>
                )}
            </TabContext>
        </div>
    )
}
