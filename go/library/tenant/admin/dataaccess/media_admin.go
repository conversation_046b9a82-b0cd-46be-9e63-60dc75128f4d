package adminDataaccess

import (
	tenantModels "contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

func GetAdminMediaData(dbCon *gorm.DB, siteId uuid.UUID) ([]tenantModels.Media, error) {
	dbQuery := dbCon
	var mediaChain = make([]tenantModels.Media, 0)

	dbQuery.
		Where(" ? = any(sites) ", siteId).
		Where(" active = true ").
		Where(" ( EXISTS (select id from content where media_id = media.id AND (settings->>'imported') IS NULL)").
		Or("NOT EXISTS(select id from content where media_id = media.id)) ").
		Order(" created desc ").
		Find(&mediaChain)

	return mediaChain, nil
}

func GetMediaById(dbCon *gorm.DB, mediaId uuid.UUID, ignoreActive bool) (tenantModels.Media, error) {
	var media tenantModels.Media
	dbQuery := dbCon

	if !ignoreActive {
		dbQuery = dbQuery.
			Where(" active = true ")
	}

	if err := dbQuery.
		Where(" id = ? ", mediaId).
		First(&media).
		Error; err != nil {
		return media, err
	}
	return media, nil
}

func DeleteMedia(dbCon *gorm.DB, queryModel tenantModels.Media) (tenantModels.Media, error) {
	queryModel.Deleted = time.Now()
	queryModel.Active = false
	return SaveMedia(dbCon, queryModel)
}

func SaveMedia(dbCon *gorm.DB, queryModel tenantModels.Media) (tenantModels.Media, error) {
	dbQuery := dbCon
	queryModel.Updated = time.Now()
	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return tenantModels.Media{}, nil
}

func CreateMedia(dbCon *gorm.DB, queryModel tenantModels.Media) (tenantModels.Media, error) {
	dbQuery := dbCon
	queryModel.Created = time.Now()
	if err := dbQuery.Create(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}
