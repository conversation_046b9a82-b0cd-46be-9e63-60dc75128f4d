package handlers

import (
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/user_management/pat"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

func PAT_GET(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	accountID := uuid.FromStringOrNil(p["account_id"])
	if err := ensurePermissions(w, r, accountID); err != nil {
		return
	}

	utils.WriteResultJSON(w, GetTokens(r, accountID))
}

func PAT_POST(w http.ResponseWriter, r *shared.AppContext) {
	var dto pat.PATokenDTO
	if err := binding.JSON.Bind(r.Request(), &dto); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	if err := ensurePermissions(w, r, dto.AccountID); err != nil {
		return
	}

	utils.WriteResultJSON(w, CreateToken(r, dto))
}

func PAT_DELETE(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	tokenID := uuid.FromStringOrNil(p["token_id"])
	if tokenID == uuid.Nil {
		err := errors.New("Invalid id: " + tokenID.String())
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var token pat.PAToken
	if err := r.TenantDatabase().First(&token, "id = ?", tokenID).Error; err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if err := ensurePermissions(w, r, token.AccountID); err != nil {
		return
	}

	utils.WriteResultJSON(w, DeleteToken(r, tokenID))
}

// ensurePermissions checks if the accountID is valid. Only admins and the account owner can edit/access tokens.
func ensurePermissions(w http.ResponseWriter, r *shared.AppContext, accountID uuid.UUID) error {
	if accountID == uuid.Nil {
		err := errors.New("Invalid id: " + accountID.String())
		utils.WriteResponseJSON(w, nil, err)
		return err
	}

	if !(r.Account().IsAdmin || r.Account().ID == accountID) {
		err := utils.UnauthorizedError{Message: "You can't edit tokens for this account. Your account ID: " + r.Account().ID.String() + ", token account ID: " + accountID.String() + "."}
		utils.WriteResponseJSON(w, nil, err)
		return err
	}
	return nil
}
