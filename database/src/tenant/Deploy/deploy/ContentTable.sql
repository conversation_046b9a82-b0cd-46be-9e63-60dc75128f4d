-- Deploy contentmanagerapp_DeploySchema:ContentTable to pg

BEGIN;

    CREATE TABLE content
    (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
		type content_type,
		owner uuid,
		publisher uuid,
		title CHARACTER VARYING(256),
		content text,
		data jsonb,
		structure jsonb,
		route CHARACTER VARYING(256),
		path ltree,
		pagelayout uuid,
		created TIMESTAMP WITH TIME ZONE DEFAULT now(),
		updated TIMESTAMP WITH TIME ZONE,
		deleted TIMESTAMP WITH TIME ZONE,
		privacy_level integer,
		approved boolean DEFAULT TRUE,
		active boolean DEFAULT TRUE,
		sites uuid[],
		tags uuid[],
		CONSTRAINT pk_content PRIMARY KEY(id),
        CONSTRAINT fk_owner_id FOREIG<PERSON> KEY (owner) REFERENCES account(id) ON DELETE NO ACTION,
        CONSTRAINT fk_publisher_id FOREIG<PERSON>EY (publisher) REFERENCES account(id) ON DELETE NO ACTION
    )WITH (
		OIDS=FALSE
	);

    CREATE INDEX ix_content_path ON content (path);
    CREATE INDEX ix_content_route ON content (route);
    CREATE INDEX ix_content_type ON content (type);

    GRANT ALL ON TABLE content to contentmanager_application_user;

COMMIT;
