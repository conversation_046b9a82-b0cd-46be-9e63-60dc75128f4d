package admin

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/cryptor"
	"net/http"
)

type (
	Params struct {
		Email string `binding:"required,email"`
	}
)

func AddAdminForms(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v2/forms", func(router httpService.Router) {
		router.Post("/encrypt", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody Params
		}) {
			if encrypted, err := cryptor.Encrypt(params.FromBody.Email); err != nil {
				utils.WriteResponseJSON(w, nil, err)
			} else {
				utils.WriteResponseJSON(w, Params{
					Email: encrypted,
				}, nil)
			}
		})

		router.Post("/decrypt", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody Params
		}) {
			if decrypted, err := cryptor.Decrypt(params.FromBody.Email); err != nil {
				utils.WriteResponseJSON(w, nil, err)
			} else {
				utils.WriteResponseJSON(w, Params{
					Email: decrypted,
				}, nil)
			}
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware())
	return r
}
