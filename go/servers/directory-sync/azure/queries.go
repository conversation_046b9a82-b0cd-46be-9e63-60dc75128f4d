package azurexx

import (
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/identity"
	"gorm.io/gorm"
	"strings"
)

// getMapOfEmailToAccounts
// Gets all Directory based accounts and attempts to omit native accounts.
// Issue: Native accounts aren't standardized, some have no passwords, some have passwords (from debugging?)
// Try:  to get all accounts, and then to filter out the ones with a password at the end.
func getMapOfEmailToAccounts(db *gorm.DB) (map[string]identity.Account, error) {
	var accounts []identity.Account
	if err := db.Find(&accounts).Error; err != nil {
		return nil, err
	}
	return slicexx.AsMap(accounts, func(entity identity.Account) string {
		return strings.ToLower(entity.Email)
	}), nil
}

// // getExternalIdsFromSecurityGroups
// // Returns a distinct list of all External Ids found in security_group.external_id_list
func getExternalIdsFromSecurityGroups(db *gorm.DB) ([]string, error) {
	var externalIds []string
	db.Table(identity.SecurityGroup{}.TableName()).
		Select("unnest(external_id_list)").
		Where("active").
		Where("type = ?", identity.EXTERNAL).
		Find(&externalIds)
	if db.Error != nil {
		return nil, db.Error
	}
	return slicexx.Distinct(externalIds), nil
}
