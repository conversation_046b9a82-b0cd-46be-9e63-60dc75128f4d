package commonServices

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/config"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"log"
	"testing"
)

func BenchmarkDownload(b *testing.B) {
	//tenancyServiceConfig := config.IParseConfigAdapter().ParseConfig()
	//cache.ICacheAdapter().CacheObject(conf.TenancyConfigCacheKey, tenancyServiceConfig, false)
	config.Init()
	tenantID := uuid.FromStringOrNil("eab04800-b8c8-460e-a002-ea27b7263406")

	v, err := Download(tenantID, "9525b433-8e2c-43b8-be8b-6d7ce8e5fc39", commonModels.UploadDocument)
	if err != nil {
		msg := ""
		if utils.IsErrorNotFound(err) {
			msg += fmt.Sprintf("error not found: \n [%s]", err.Error())
		} else if utils.IsErrorS3Timeout(err) {
			msg += fmt.Sprintf("timeout: \n [%s]", err.Error())
		} else {
			msg += err.Error()
		}
		return
	}
	if err != nil {
		log.Fatalln(err.Error())
	}
	log.Println(len(v))
}
