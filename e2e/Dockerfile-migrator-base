FROM ubuntu:22.04

# Install necessary tools
RUN apt-get update && apt-get install -y \
    curl \
    postgresql-client \
    unzip \
    build-essential \
    python3-pip \
    python3-dev \
    cpanminus \
    perl \
    perl-doc \
    libdbd-pg-perl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip3 install --no-cache-dir awscli \
    && cpanm --quiet --notest App::Sqitch@1.1.0

ENV TZ=Etc/UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
