package adminDataaccess

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/image_crop_size"
	"github.com/satori/go.uuid"
)

type Params struct {
	pagx.Query
	Search             string
	SiteID             *uuid.UUID  `binding:"required"`
	Tags               []uuid.UUID `json:"Tags[]"`
	ImageCropSizeNames []string    `json:"ImageCropSizeNames[]"`
}

func SearchMedia(rq *shared.AppContext, q Params) result.Result[pagx.Paginated[commonModels.Media]] {
	var pag pagx.Paginated[commonModels.Media]

	tx := rq.TenantDatabase()
	tx = tx.Where("active = true").
		Where("origin is null")

	if len(q.Tags) > 0 {
		tx = tx.Where(pgxx.ArrayHasAll("tags", q.Tags))
	}

	if len(q.ImageCropSizeNames) > 0 {
		var ids []uuid.UUID
		if err := rq.TenantDatabase().Table(image_crop_size.ImageCropSize{}.TableName()).Where("active").
			Where(pgxx.FieldInArray("name", q.ImageCropSizeNames)).
			Pluck("id", &ids).Error; err != nil {
			return result.Error(err, pag)
		}
		if len(ids) > 0 {
			tx = tx.Where(pgxx.ArrayHasAny("image_crop_size_ids", ids))
		}
	}

	if len(q.Search) > 0 {
		tx = tx.Where(commonModels.Media{}.SearchQuery(), "%"+q.Search+"%")
	}

	if q.SiteID != nil {
		tx = tx.Where(SitesQuery(*q.SiteID))
	}

	tx = tx.Order("created desc")

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}
