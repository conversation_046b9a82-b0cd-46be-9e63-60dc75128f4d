package adminControllers

import (
	"bytes"
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/permissions/evaluators"
	"contentmanager/pkgs/multitenancy"
	"encoding/base64"
	"encoding/json"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
	"strconv"
	"strings"
	"time"
)

/*************************************************************************************************************
API Document Controller
*************************************************************************************************************/

type (
	DocumentController struct{}
	BulkDocuments      struct {
		Ids         []uuid.UUID `json:"ids"`
		Destination uuid.UUID   `json:"folderId"`
	}
	FolderViewModel struct {
		FolderFilename string
		FolderContents []commonModels.Document
		Parent         commonModels.Document
		Error          error
		CurrentTitle   string
	}
	DocumentResultSet struct {
		TotalRecords int `json:"total_records"`
		Offset       int `json:"offset"`
		Limit        int `json:"limit"`
	}
	DocumentResponse struct {
		Results   []commonModels.Document `json:"results"`
		ResultSet DocumentResultSet       `json:"resultset"`
	}
)

// Temporary.. I hope.
func (bd *BulkDocuments) UnmarshalJSON(b []byte) error {
	tmp := struct {
		Ids         []uuid.UUID `json:"ids"`
		Destination string      `json:"folderId"`
	}{}
	if err := json.Unmarshal(b, &tmp); err != nil {
		return err
	}
	if len(tmp.Ids) == 0 {
		return errors.New("empty []uuid provided")
	}
	bd.Ids = tmp.Ids
	bd.Destination = uuid.FromStringOrNil(tmp.Destination)
	return nil
}

// -------------------- Admin --------------------  //
func (dc DocumentController) GetAdminDocuments(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication

	tenantDB := r.TenantDatabase()
	var documentChain []commonModels.Document
	var folderId uuid.UUID
	var totalRecords int
	var err error
	tags := []string{}
	// TODO => change to tags

	r.Request().ParseForm()
	offsetStr := r.Request().Form.Get("offset")
	limitStr := r.Request().Form.Get("limit")
	filename := r.Request().Form.Get("filename")
	title := r.Request().Form.Get("title")
	documentType := r.Request().Form.Get("documentType")
	folderIdStr := r.Request().Form.Get("folderId")

	if r.Request().Form["tags[]"] != nil {
		tags = r.Request().Form["tags[]"]
	}
	if len(folderIdStr) > 0 {
		folderId, _ = uuid.FromString(folderIdStr)
	}
	ignoreParams := converters.GetBooleanValueFromQueryString(r.Request().Form["ignoreParameters"], true)
	offset, _ := strconv.Atoi(offsetStr)
	limit, _ := strconv.Atoi(limitStr)

	documentChain, totalRecords, err = adminServices.GetAdminDocuments(tenantDB, r.CurrentSiteID(), folderId, tags, filename, title, documentType, offset, limit, ignoreParams)
	if err != nil {
		utils.ResponseJson(w, utils.Message("Error retrieving documents"), http.StatusBadRequest)
		return
	}

	response := dc.GetResultResponse(documentChain, offset, limit, totalRecords)
	utils.ResponseJson(w, response, http.StatusOK)
}

func (dc DocumentController) GetAdminDocumentById(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	//Requires Authentication

	var documentId uuid.UUID
	if len(p["id"]) > 0 {
		idStr := p["id"]
		documentId = uuid.FromStringOrNil(idStr)
	}

	var document commonModels.Document
	if err := r.TenantDatabase().First(&document, "id = ?", documentId).Error; err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResponseJSON(w, document, nil)
}

func (dc DocumentController) GetAdminDocumentAncestorsById(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	//Requires Authentication

	var documentId uuid.UUID
	if len(p["id"]) > 0 {
		idStr := p["id"]
		documentId = uuid.FromStringOrNil(idStr)
	}
	tenantDB := r.TenantDatabase()

	documents, err := adminServices.GetDocumentAncestorsById(tenantDB, documentId, r.CurrentSiteIDNullable())
	if err != nil {
		utils.ResponseJson(w, utils.Message("Error retrieving document ancestors"), http.StatusBadRequest)
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("Error retrieving document ancestors")
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(documents)
}

type CreateDocument struct {
	ID           uuid.UUID                 `json:"id"`
	Data         string                    `json:"data"`
	Thumbnail    string                    `json:"thumbnail"`
	Filename     string                    `json:"filename"`
	Title        string                    `json:"title"`
	Path         string                    `json:"path"`
	PrivacyLevel int                       `json:"privacyLevel"`
	Type         commonModels.DocumentType `json:"type"`
	Sites        dbDriver.PgUUIDArray      `json:"sites"`
	DepartmentID *uuid.UUID                `json:"department_id"`
	Tags         dbDriver.PgUUIDArray      `json:"tags"`
	Created      time.Time                 `json:"created"`
	Updated      time.Time                 `json:"updated"`
	Deleted      time.Time                 `json:"deleted"`
	Active       bool                      `json:"active"`
}

func (c CreateDocument) GetSites() []uuid.UUID {
	return c.Sites
}

func (c CreateDocument) GetDepartmentID() *uuid.UUID {
	return c.DepartmentID
}

func (c CreateDocument) GetScopeEntity() string {
	return "cm.document." + string(c.Type)
}

func (c CreateDocument) GetType() string {
	return string(c.Type)
}

func (c CreateDocument) GetID() uuid.UUID {
	return c.ID
}

func (dc DocumentController) PostAdminDocument(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	var createDocument CreateDocument
	if err := evaluators.ForCreate(r, &createDocument); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if strings.Contains(createDocument.Path, ".") {
		segments := strings.Split(createDocument.Path, ".")
		folderID := uuid.FromStringOrNil(segments[len(segments)-2])
		if err := evaluators.ForActionByID(r, &commonModels.Document{}, folderID, "update"); err != nil {
			utils.WriteResponseJSON(w, nil, err)
			return
		}
	}

	if createDocument.Type == "document" {
		_, err := commonModels.DocumentFilenameIsValid(createDocument.Filename)
		if err != nil {
			utils.WriteResponseJSON(w, nil, err)
			return
		}

		_, err = commonModels.DocumentTitleIsValid(createDocument.Title)
		if err != nil {
			utils.WriteResponseJSON(w, nil, err)
			return
		}

	}

	tenantDB := r.TenantDatabase()
	var UploadMedia commonModels.UploadMedia = commonModels.UploadMedia{
		ID:        createDocument.ID,
		Data:      createDocument.Data,
		Thumbnail: createDocument.Thumbnail,
	}
	now := time.Now()
	var document commonModels.Document = commonModels.Document{
		ID:           createDocument.ID,
		Filename:     createDocument.Filename,
		Title:        createDocument.Title,
		Path:         createDocument.Path,
		PrivacyLevel: createDocument.PrivacyLevel,
		Type:         createDocument.Type,
		Sites:        createDocument.Sites,
		DepartmentID: createDocument.DepartmentID,
		Tags:         createDocument.Tags,
		Created:      now,
		Updated:      now,
		Active:       true,
	}

	if err := commonServices.UploadDocument(UploadMedia, r.TenantID()); err == nil || len(UploadMedia.Data) == 0 {
		if d, e := adminServices.CreateDocument(tenantDB, document); e == nil {

			onDocumentChange(r, document, UploadMedia.Data)
			json.NewEncoder(w).Encode(d)
			return
		}
	}

	utils.ResponseJson(w, utils.Message("not able to create provided document"), http.StatusBadRequest)
}

func (dc DocumentController) UpdateAdminDocument(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication

	var editDocument commonModels.Document
	var previousDocument commonModels.Document
	if err := evaluators.ForUpdate(r, &editDocument, &previousDocument); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if document, err := adminServices.UpdateDocument(r.TenantDatabase(), editDocument); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			http.NotFound(w, r.Request())
			return
		} else {
			http.Error(w, err.Error(), 417)
			return
		}
	} else {
		if document.Type == commonModels.File {
			onDocumentChange(r, document, "")
		}

		json.NewEncoder(w).Encode(document)
		return
	}
}

func (dc DocumentController) UpdateAdminDocumentsLocation(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	var moveTo BulkDocuments
	if err := binding.JSON.Bind(r.Request(), &moveTo); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var documents []commonModels.Document
	var ids = append([]uuid.UUID{}, moveTo.Ids...)
	if moveTo.Destination != uuid.Nil {
		ids = append(moveTo.Ids, moveTo.Destination)
	}
	if err := evaluators.ForActionByDBIDs(r, &documents, ids, "update"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	folder, _ := slicexx.FindFirst(documents, func(d commonModels.Document) bool {
		return moveTo.Destination == d.ID
	})
	docs := slicexx.Filter(documents, func(d commonModels.Document) bool {
		return moveTo.Destination != d.ID
	})
	now := time.Now()
	var pathPrefix string
	if len(folder.Path) > 0 {
		pathPrefix = folder.Path + "."
	}
	for i, _ := range docs {
		docs[i].ParentID = moveTo.Destination
		docs[i].Path = pathPrefix + utils.SanitizeLTree(docs[i].ID.String())
		docs[i].Updated = now
	}
	if err := r.TenantDatabase().Save(&docs).Error; err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResponseJSON(w, docs, nil)
}

func (dc DocumentController) UpdateDocumentBinaryById(w http.ResponseWriter, r *shared.AppContext, p httpService.Params, factory multitenancy.AccessorFactory) {
	//Requires Authentication
	tenantDB := r.TenantDatabase()
	var documentId uuid.UUID
	var doc commonModels.Document
	var uploadMedia commonModels.UploadMedia

	if len(p["id"]) > 0 {
		idStr := p["id"]
		documentId = uuid.FromStringOrNil(idStr)
	}

	if err := evaluators.ForActionByID(r, &doc, documentId, "update"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if err := binding.JSON.Bind(r.Request(), &uploadMedia); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var document commonModels.Document
	document, err := adminServices.ReplaceDocument(tenantDB, r.TenantID(), doc, uploadMedia)

	if err != nil {
		utils.WriteResponseJSON(w, document, err)
		return
	}

	//go func(tenantID uuid.UUID, document commonModels.Document, factory multitenancy.AccessorFactory) {
	//	logging.RootLogger().Info().Msgf("[CreateInvalidationForResizer] Invalidating document cache %s", document.ID)
	//	mta := factory.Accessor(context.Background())
	//
	//	if _, err := amzn.CreateInvalidationForTenant(mta.TenancyDB(), tenantID,
	//		[]string{
	//			fmt.Sprintf("/documents/%s*", document.ID),
	//			fmt.Sprintf("/documents/%s*", url.QueryEscape(document.Filename))}); err != nil {
	//		logging.RootLogger().Warn().Err(err).Msgf("[CreateInvalidationForTenant] Can't invalidate cache for tenantID/Document: %v, %v, %v", tenantID, document.ID, document.Filename)
	//	}
	//}(r.TenantID(), document, factory)

	if document.Type == commonModels.File {
		onDocumentChange(r, document, uploadMedia.Data)
	}

	utils.WriteResponseJSON(w, document, nil)
	return
}

func (dc DocumentController) DeleteAdminDocuments(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	r.Request().ParseForm()
	documentIds := r.Request().Form["ids"]

	var docs []commonModels.Document
	ids := slicexx.Select(documentIds, func(id string) uuid.UUID {
		if u, e := uuid.FromString(id); e != nil {
			panic(e)
		} else {
			return u
		}
	})
	if err := evaluators.ForActionByDBIDs(r, &docs, ids, "delete"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	now := time.Now()
	for i, _ := range docs {
		docs[i].Active = false
		docs[i].Updated = now
		docs[i].Deleted = now

	}
	if err := r.TenantDatabase().Save(&docs).Error; err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	/*
		onDocumentChange has UpdateIndexForDocumentID, which will clear out existing search_data due to active = false.
			index_documents.go > line 151 (upsertIndexDataForDocumentID)
		CDN path invalidation on delete makes sense, so it seems like we can just call `onDocumentChange` for delete path.
	*/

	//index.CleanupDocuments(r)
	for _, deactivated := range docs {
		onDocumentChange(r, deactivated, "")
	}

	utils.WriteResponseJSON(w, nil, nil)
	return
}

// -------------------- Utility --------------------  //
func (dc DocumentController) GetResultResponse(documentChain []commonModels.Document, offset, limit int, totalRecords int) map[string]interface{} {
	var results map[string]interface{}

	resultset := DocumentResultSet{
		TotalRecords: totalRecords,
		Offset:       offset,
		Limit:        limit,
	}
	response := DocumentResponse{
		Results:   documentChain,
		ResultSet: resultset,
	}

	marshalledResponse, mrErr := json.Marshal(response)
	if mrErr != nil {
		return results
	}
	json.Unmarshal(marshalledResponse, &results)
	return results
}

func (dc DocumentController) DecodeB64(binary []byte) ([]byte, error) {
	str := bytes.NewBuffer(binary).String()
	if strings.Index(str, "base64,") == -1 {
		return []byte{}, errors.New("error decoding base64")
	}
	pdfStr := str[strings.Index(str, "base64,")+7:]
	imgBase64Decoded, _ := base64.StdEncoding.DecodeString(pdfStr)
	return imgBase64Decoded, nil
}
