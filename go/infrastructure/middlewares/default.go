package middlewares

import "contentmanager/library/httpService"

// <PERSON><PERSON><PERSON> creates a default Middleware with some basic default middleware - middleware.Logger, middleware.Recovery and middleware.Static.
// De<PERSON>ult also maps default.Routes as a service.
func Default() *httpService.DefaultMiddleware {
	r := httpService.NewRouter()
	m := httpService.New()
	m.Use(AppVersion())
	m.Use(CaseSensitivityRedirect())
	m.Use(httpService.SharedAppContext())
	m.Use(Recovery()) // TODO: Recovery should be before SharedAppContext
	m.MapTo(r, (*httpService.Routes)(nil))
	m.Action(r.Handle)
	return &httpService.DefaultMiddleware{m, r}
}
