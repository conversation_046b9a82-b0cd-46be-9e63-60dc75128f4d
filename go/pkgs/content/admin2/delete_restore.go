package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
)

func Delete(r *shared.AppContext, fromDB content.Content) result.EmptyResult {
	if fromDB.Workspace != "live" {
		// TODO: @Anatoly update EffectiveIn
		return result.CheckEmpty(r.TenantDatabase().Delete(&fromDB).Error)
	}

	subPath := strings.ReplaceAll(fromDB.ID.String(), "-", "")
	var count int64
	if err := r.TenantDatabase().Model(&content.Content{}).Where("active and id != ? and path::text like ?", fromDB.ID, "%"+subPath+"%").Count(&count).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if count > 0 {
		return result.ErrorEmpty(fmt.Errorf("Cannot delete template because it is used by %v active content. ", count))
	}

	fromDB.Active = false
	// fromDB.EffectiveIn = shared2.EffectiveIn(fromDB.Workspace, r.WorkspacesInTenant())
	fromDB.PublishPeriod.PublishAt = nil
	fromDB.Publisher = r.Account().ID
	fromDB.Updated = r.AppTime().NowUTC()

	return result.CheckEmpty(r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		if err := tx.Save(&fromDB).Error; err != nil {
			return err
		}
		if err := tx.Exec(" DELETE FROM content WHERE id = ? AND workspace != 'live'", fromDB.ID).Error; err != nil {
			return err
		}

		onContentChanged(r, fromDB)
		return nil
	}))
}

func Restore(r *shared.AppContext, fromDB content.Content) result.EmptyResult {
	if fromDB.Workspace != "live" {
		return result.ErrorEmpty(fmt.Errorf("Cannot restore content from non-live workspace. "))
	}

	fromDB.Active = true
	fromDB.Publisher = r.Account().ID
	fromDB.Updated = r.AppTime().NowUTC()

	if err := r.TenantDatabase().Save(&fromDB).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := fromDB.StartOrExtendSession(reservation.ReservableParams{}, r.Account()); err != nil {
		return result.ErrorEmpty(err)
	}

	onContentChanged(r, fromDB)
	return result.SuccessEmpty()
}

func Expire(r *shared.AppContext, id uuid.UUID, workspace string) result.EmptyResult {
	var dbContent content.Content
	if err := r.TenantDatabase().Where("id = ? AND workspace = ?", id, workspace).First(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := dbContent.StartOrExtendSession(reservation.ReservableParams{}, r.Account()); err != nil {
		return result.ErrorEmpty(err)
	}

	if dbContent.Type == commonModels.Template {
		subPath := strings.ReplaceAll(dbContent.ID.String(), "-", "")
		var count int64
		if err := r.TenantDatabase().Model(&content.Content{}).Where("active and id != ? and path::text like ?", dbContent.ID, "%"+subPath+"%").Count(&count).Error; err != nil {
			return result.ErrorEmpty(err)
		}
		if count > 0 {
			return result.ErrorEmpty(fmt.Errorf("Cannot delete template because it is used by %v active content. ", count))
		}
	}

	now := r.AppTime().NowUTC()

	dbContent.ExpireAt = &now
	dbContent.Publisher = r.Account().ID
	dbContent.Updated = r.AppTime().NowUTC()
	if err := r.TenantDatabase().Save(&dbContent).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	onContentChanged(r, dbContent)

	return result.SuccessEmpty()
}
