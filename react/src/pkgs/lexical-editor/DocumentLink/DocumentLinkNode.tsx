import {
    $applyNodeReplacement,
    DecoratorNode,
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    LexicalNode,
    NodeKey,
    SerializedLexicalNode,
    Spread
} from 'lexical'

import React, { Suspense } from 'react'

export const domNodeDocumenLinkNodeAttrId = 'data-lexical-document-id'
const domNodeDocumentLinkNodeHtmlTag = 'ie-document'
export const DocumentLinkNodeType = 'document-link-node-type'

function getDocumentLinkNodeHtml(documentId: string) {
    const element = document.createElement(domNodeDocumentLinkNodeHtmlTag)
    element.setAttribute(domNodeDocumenLinkNodeAttrId, documentId)

    return element
}

const DocumentLinkNodeComponent = React.lazy(() => import('./DocumentLinkNodeComponent'))

export type SerializedDocumentLinkNode = Spread<
    {
        documentId: string
    },
    SerializedLexicalNode
>

function convertDocumentLinkNodeElement(domNode: HTMLElement): null | DOMConversionOutput {
    const documentId = domNode.getAttribute(domNodeDocumenLinkNodeAttrId) || undefined
    if (!documentId) {
        console.error(`domNode missing document id`)
        return null
    }

    const node = $createDocumentLinkNode(documentId)
    return { node }
}

export class DocumentLinkNode extends DecoratorNode<JSX.Element> {
    __documentId: string

    constructor(documentId: string, key?: NodeKey) {
        super(key)
        this.__documentId = documentId
    }

    setDocumentId(documentId: string) {
        const writable = this.getWritable()
        writable.__documentId = documentId
    }

    static getType(): string {
        return DocumentLinkNodeType
    }

    static clone(node: DocumentLinkNode): DocumentLinkNode {
        return new DocumentLinkNode(node.__documentId, node.__key)
    }

    static importJSON(serializedNode: SerializedDocumentLinkNode): DocumentLinkNode {
        const node = $createDocumentLinkNode(serializedNode.documentId)
        return node
    }

    exportJSON(): SerializedDocumentLinkNode {
        return {
            type: 'document-link-node-type',
            version: 1,
            documentId: this.__documentId
        }
    }

    exportDOM(): DOMExportOutput {
        return { element: getDocumentLinkNodeHtml(this.__documentId) }
    }

    static importDOM(): DOMConversionMap | null {
        return {
            [domNodeDocumentLinkNodeHtmlTag]: (domNode: HTMLElement) => {
                if (!domNode.hasAttribute(domNodeDocumentLinkNodeHtmlTag)) {
                    return null
                }

                const conversionTest = convertDocumentLinkNodeElement(domNode)
                if (!conversionTest) {
                    console.warn('DocumentLinkNode failed to convert DOM to Node')
                }

                return {
                    conversion: convertDocumentLinkNodeElement,
                    priority: 0
                }
            }
        }
    }

    createDOM(config: EditorConfig): HTMLElement {
        const span = document.createElement('span')

        return span
    }

    updateDOM(): false {
        return false
    }

    getTextContent(_includeInert?: boolean | undefined, _includeDirectionless?: false | undefined): string {
        return getDocumentLinkNodeHtml(this.__documentId).outerHTML
    }

    decorate(): JSX.Element {
        return (
            <Suspense fallback={null}>
                <DocumentLinkNodeComponent nodeKey={this.__key} documentId={this.__documentId} />
            </Suspense>
        )
    }
}

export function $createDocumentLinkNode(documentId: string): DocumentLinkNode {
    return $applyNodeReplacement(new DocumentLinkNode(documentId))
}

export function $isDocumentLinkNode(node: DocumentLinkNode | LexicalNode | null | undefined): node is DocumentLinkNode {
    return node instanceof DocumentLinkNode
}
