package tags

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"net/url"
)

func AddTags(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("", func(router httpService.Router) {
		router.Get("/sys/v1/tagged/:id/latest", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			var doc commonModels.Document
			if err := r.TenantDatabase().Select("id, filename").Where("active").
				Where("? = any(tags)", params.FromPath.ID).
				Where("? = any(sites)", r.CurrentSiteID()).
				Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0)", r.PublicAccount().PrivacyLevel).Order("created desc").First(&doc).Error; err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			path := "/documents/" + doc.ID.String() + "/" + url.PathEscape(doc.Filename)
			http.Redirect(w, r.Request(), path, http.StatusMovedPermanently)
		})
	}, bindauth.BindParamsMiddleware())

	r.Group("/api/v2/tag", func(router httpService.Router) {
		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery SearchQuery
		}) {
			utils.WriteResultJSON(w, Search(r, params.FromQuery))
		})

		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody TagDTO
		}) {
			utils.WriteResultJSON(w, Create(r, params.FromBody))
		})

		router.Put("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromBody TagDTO
			FromDB   Tag
		}) {
			utils.WriteResultJSON(w, Update(r, params.FromPath.ID, params.FromBody))
		})

		router.Delete("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB Tag
		}) {
			utils.WriteResultJSON(w, Delete(r, params.FromPath.ID))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())

	return r
}
