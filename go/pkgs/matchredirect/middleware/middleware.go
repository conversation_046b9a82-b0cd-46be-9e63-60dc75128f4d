package middleware

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/pkgs/matchredirect"
	"contentmanager/pkgs/multitenancy"
	"net/http"
)

func MatchRedirectMiddleware(mta multitenancy.Accessor) httpService.Handler {
	instance := matchredirect.Initialize(mta)

	return func(rw http.ResponseWriter, r *shared.AppContext, c httpService.Context) {
		if to, ok := instance.Match(r.TenantID(), r.Request()); ok {
			http.Redirect(rw, r.Request(), to.String(), http.StatusMovedPermanently)
			return
		}

		c.Next()
	}
}
