package jsonxx

import "testing"

func Test_FlattenJSON(t *testing.T) {
	input := []byte(`
	{
		"imageList": [
			{
				"id": "cb305d6b-cbac-434f-926d-ff42d4daf276",
				"image": {
					"alt": "News Image",
					"src": "/images/ddb973ed-2a7d-51d5-8c45-ed6d8bf9c2e7"
				},
				"caption": "News Image"
			},
			{
				"id": "8fdcb272-da69-506a-977a-90a683d579ec",
				"image": {
					"alt": "Image 2",
					"src": "/images/f4ed0d94-91fd-5ec6-ae37-7d79044d2622"
				},
				"caption": "Caption"
			}
		],
		"mainContent": {
			"id": "2f52cc3a-f589-4acf-bc00-aaaa32d75546",
			"lexical": {
				"html": "<section>\n...</section>"
			}
		}
	}
	`)

	expected := map[string]interface{}{
		"imageList.0.id":           "cb305d6b-cbac-434f-926d-ff42d4daf276",
		"imageList.0.image.alt":    "News Image",
		"imageList.0.image.src":    "/images/ddb973ed-2a7d-51d5-8c45-ed6d8bf9c2e7",
		"imageList.0.caption":      "News Image",
		"imageList.1.id":           "8fdcb272-da69-506a-977a-90a683d579ec",
		"imageList.1.image.alt":    "Image 2",
		"imageList.1.image.src":    "/images/f4ed0d94-91fd-5ec6-ae37-7d79044d2622",
		"imageList.1.caption":      "Caption",
		"mainContent.id":           "2f52cc3a-f589-4acf-bc00-aaaa32d75546",
		"mainContent.lexical.html": "<section>\n...</section>",
	}

	result, err := FlattenJSON(input)
	if err != nil {
		t.Errorf("FlattenJSON returned an error: %v", err)
	}

	for k, v := range expected {
		if result[k] != v {
			t.Errorf("For key %s, expected %v but got %v", k, v, result[k])
		}
	}

	for k, v := range result {
		if expected[k] != v {
			t.Errorf("Unexpected key-value pair in result: %s: %v", k, v)
		}
	}
}

func Test_FlattenJSONWithNestedArrays(t *testing.T) {
	input := []byte(`
	{
		"nested": {
			"array": [
				[1, 2, 3],
				[4, 5, 6]
			]
		}
	}
	`)

	expected := map[string]interface{}{
		"nested.array.0.0": float64(1),
		"nested.array.0.1": float64(2),
		"nested.array.0.2": float64(3),
		"nested.array.1.0": float64(4),
		"nested.array.1.1": float64(5),
		"nested.array.1.2": float64(6),
	}

	result, err := FlattenJSON(input)
	if err != nil {
		t.Errorf("FlattenJSON returned an error: %v", err)
	}

	for k, v := range expected {
		if result[k] != v {
			t.Errorf("For key %s, expected %v but got %v", k, v, result[k])
		}
	}

	for k, v := range result {
		if expected[k] != v {
			t.Errorf("Unexpected key-value pair in result: %s: %v", k, v)
		}
	}
}

func Test_FlattenJSONWithEmptyInput(t *testing.T) {
	input := []byte(`{}`)

	result, err := FlattenJSON(input)
	if err != nil {
		t.Errorf("FlattenJSON returned an error: %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected empty result, but got: %v", result)
	}
}

func Test_FlattenJSONWithInvalidInput(t *testing.T) {
	input := []byte(`{"invalid": json}`)

	_, err := FlattenJSON(input)
	if err == nil {
		t.Error("Expected an error for invalid JSON, but got nil")
	}
}

func Test_FlattenJSONEmpty(t *testing.T) {
	input := []byte(`{}`)

	result, err := FlattenJSON(input)
	if err != nil {
		t.Errorf("FlattenJSON returned an error: %v", err)
	}
	if len(result) != 0 {
		t.Errorf("Expected empty result, but got: %v", result)
	}
}
