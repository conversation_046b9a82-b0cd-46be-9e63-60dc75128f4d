#!/bin/bash

# Set the environment variables if not already set
export PGHOST="***********"
export PGUSER="postgres"
export PGPASSWORD=${PGPASSWORD}

echo "Connecting to database: $PGHOST"

# Query the cm_multitenancy database to get the server and host column values from the tenant table
if ! psql_output=$(psql -h $PGHOST -t -A -F '|' -d cm_multitenancy -c "SELECT server, host FROM tenant;"); then
    echo "Failed to fetch server and host list from cm_multitenancy."
    exit 1
fi

# Loop through each server and host and perform operations
echo "$psql_output" | while IFS='|' read -r server host; do
    echo "----- $server at $host ----"

    echo "Cleaning content_history..."
    psql -t -A -h "$host" -d "$server" -c "DELETE FROM content_history WHERE publisher = '45f06f48-a93c-414e-b9a0-7582e0abc085';"
    psql -t -A -h "$host" -d "$server" -c "VACUUM FULL content_history;"
    psql -t -A -h "$host" -d "$server" -c "ANALYZE content_history;"

    echo "Cleaning content..."
    psql -t -A -h "$host" -d "$server" -c "VACUUM FULL content;"
    psql -t -A -h "$host" -d "$server" -c "ANALYZE content;"

    echo "Cleaning audit_record..."
    psql -t -A -h "$host" -d "$server" -c "DELETE FROM audit_record WHERE created_at < NOW() - INTERVAL '1 month';"
    psql -t -A -h "$host" -d "$server" -c "VACUUM FULL audit_record;"
    psql -t -A -h "$host" -d "$server" -c "ANALYZE audit_record;"
done
