package seed_notifications

import (
	"contentmanager/pkgs/notifications/models"
	"context"
	"github.com/brianvoe/gofakeit/v6"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

func SeedSubscriptions(ctx context.Context, db *gorm.DB) {
	var sIDs []uuid.UUID
	if err := db.Table("subscriber").Pluck("id", &sIDs).Error; err != nil || len(sIDs) == 0 {
		panic("No subscribers")
	}

	var tIDs []uuid.UUID
	if err := db.Table("topic").Pluck("id", &tIDs).Error; err != nil || len(tIDs) == 0 {
		panic("No topics")
	}

	for _, s := range sIDs {
		ssnn := []models.Subscription{}
		for _, t := range tIDs {
			if gofakeit.IntRange(0, 100) < 10 {
				continue
			}
			sn := models.Subscription{
				SubscriberID: s,
				TopicId:      t,
				Subscribed:   gofakeit.IntRange(0, 100) < 10,
			}
			sn.Track(gofakeit.DateRange(time.Now().Add(-2*30*24*time.Hour), time.Now().Add(-30*24*time.Hour)), s)
			ssnn = append(ssnn, sn)
		}
		if err := db.Create(&ssnn).Error; err != nil {
			panic(err)
		}
	}
}
