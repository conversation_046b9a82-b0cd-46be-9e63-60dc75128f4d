package msgraph

import (
	"strings"
)

// Groups represents multiple Group-instances.
type Groups []Group

func (g Groups) String() string {
	var groups = make([]string, len(g))
	for i, calendar := range g {
		groups[i] = calendar.String()
	}
	return "Groups(" + strings.Join(groups, " | ") + ")"
}

// setGraphClient sets the GraphClient within that particular instance. Hence it's directly created by GraphClient
func (g Groups) setGraphClient(gC *GraphClient) Groups {
	for i := range g {
		g[i].setGraphClient(gC)
	}
	return g
}

// GetByDisplayName returns the Group obj of that array whose DisplayName matches
// the given name. Returns an ErrFindGroup if no group exists that matches the given
// DisplayName.
func (g Groups) GetByDisplayName(displayName string) (Group, error) {
	for _, group := range g {
		if group.DisplayName == displayName {
			return group, nil
		}
	}
	return Group{}, ErrFindGroup
}

// ====================================== //

// GroupHandler -
type GroupHandler struct {
	CurrentPage Groups `json:"value"`
	Pager
}

func (gh *GroupHandler) Next() error {
	err := gh.Pager.NextPage(&gh)
	if err != nil {
		gh.CurrentPage = make(Groups, 0)
		return err
	}
	gh.setGraphClient(gh.Client)
	return nil
}
func (gh *GroupHandler) setGraphClient(g *GraphClient) {
	gh.Client = g
	gh.CurrentPage.setGraphClient(g)
}

// GroupMemberHandler -
type GroupMemberHandler struct {
	CurrentPage Users `json:"value"`
	Pager
}

func (gh *GroupMemberHandler) Next() error {
	err := gh.Pager.NextPage(&gh)
	if err != nil {
		gh.CurrentPage = make(Users, 0)
		return err
	}
	gh.setGraphClient(gh.Client)
	return nil
}
func (gh *GroupMemberHandler) setGraphClient(g *GraphClient) {
	gh.Client = g
	gh.CurrentPage.setGraphClient(g)
}
