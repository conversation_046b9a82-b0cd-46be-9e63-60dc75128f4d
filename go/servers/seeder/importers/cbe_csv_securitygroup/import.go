package cbe_csv_securitygroup

import (
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth/identity"
	db "contentmanager/servers/seeder/db"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"log"
	"strings"
)

const (
	test    = "************"
	prod    = "************"
	tenancy = "cm_multitenancy"
)

var (
	TestTenantID       = uuid.FromStringOrNil("bb5c9248-f544-4a16-bd77-5db2fd524623")
	ProdTenantID       = uuid.FromStringOrNil("96bee624-a07c-4913-a51e-1c1247aa3590")
	NoPermissionRoleID = uuid.FromStringOrNil("ea93794f-b9a7-417d-b5c9-651db5d19726")
	PrincipalRoleID    = uuid.FromStringOrNil("4ea1fedd-ad7f-4e93-b35b-5b9b557c17a7")
)

func Load() {
	tenancyDB := db.GetConnection(db.ProdExternalIP, db.TenancyDBName)
	tenancyDBInstance, _ := tenancyDB.DB()
	defer tenancyDBInstance.Close()

	sites, err := GetSiteMap(tenancyDB)
	if err != nil {
		fmt.Printf("failed to load tenancy data with error: [%s]", err.Error())
		return
	}

	var SiteGroups = make(SiteGroupMap, 0)
	NoPermissions(sites, SiteGroups)
	ManualGroups(SiteGroups)
	Principals(sites, SiteGroups)

	var CreateGroups = append(identity.SecurityGroups{}, TenantWideGroups()...)
	for _, site := range SiteGroups {
		if site.AllStaff.IsValid() {
			CreateGroups = append(CreateGroups, site.AllStaff)
		}
		if site.Principal.IsValid() {
			CreateGroups = append(CreateGroups, site.Principal)
		}
		if site.Approver.IsValid() {
			CreateGroups = append(CreateGroups, site.Approver)
		}
		if site.Editor.IsValid() {
			CreateGroups = append(CreateGroups, site.Editor)
		}
		if site.PermissionManager.IsValid() {
			CreateGroups = append(CreateGroups, site.PermissionManager)
		}
	}
	db := db.GetConnection(db.ProdExternalIP, "cm_cbe")
	tenantDBInstance, _ := db.DB()
	defer tenantDBInstance.Close()

	err = db.Transaction(func(tx *gorm.DB) error {
		return tx.Save(&CreateGroups).Error
	})
	if err != nil {
		panic(err)
	}

	users := Users(sites, SiteGroups)
	log.Println(len(users))

	err = db.Transaction(func(tx *gorm.DB) error {
		return tx.Save(&users).Error
	})
	if err != nil {
		panic(err)
	}

	//dbInstance, _ := tenancyDB.DB()
	//_ = dbInstance.Close()
}

func UpdateEmails() {
	tenantDB := db.GetConnection(db.ProdExternalIP, "cm_cbe")
	tenancyDBInstance, _ := tenantDB.DB()
	defer tenancyDBInstance.Close()

	accounts := []identity.Account{}
	err := tenantDB.
		Where("cardinality(external_groups) = 0").
		Where("active").
		Find(&accounts).
		Error
	if err != nil {
		panic(err)
	}
	var nameMap = map[string]identity.Account{}
	for _, account := range accounts {
		nameMap[strings.ToLower(account.Firstname+account.Lastname)] = account
	}
	for _, user := range LoadUsersToElevate() {
		username := strings.ToLower(user.FirstName + user.LastName)
		account, ok := nameMap[username]
		if !ok {
			log.Println(username + " not found")
		}
		account.Email = user.Email
		nameMap[username] = account
	}
	accounts = []identity.Account{}
	for _, account := range nameMap {
		accounts = append(accounts, account)
	}
	err = tenantDB.Transaction(func(tx *gorm.DB) error {
		return tx.Create(&accounts).Error
	})
	if err != nil {
		panic(err)
	}
}

type SiteGroupMap map[uuid.UUID]GroupCategory
type GroupCategory struct {
	Approver          identity.SecurityGroup
	Editor            identity.SecurityGroup
	PermissionManager identity.SecurityGroup
	Principal         identity.SecurityGroup
	AllStaff          identity.SecurityGroup
}

func Principals(sites map[string]uuid.UUID, SiteGroups SiteGroupMap) {
	for _, group := range LoadPrincipals() {
		if len(group.AzureID) == 0 {
			continue
		}
		siteId, ok := sites[strings.ToLower(group.GetSiteName())]
		if !ok {
			fmt.Printf("\nfailed to find siteId for name: [%s]", group.DisplayName)
			continue
		}
		v, _ := SiteGroups[siteId]
		v.Principal = identity.SecurityGroup{
			ID:              utils.GenerateSatoriUUIDFromString(group.AzureID + group.DisplayName),
			Name:            "Principals",
			Description:     "Principal Group",
			RoleID:          PrincipalRoleID,
			SiteID:          &siteId,
			Active:          true,
			Type:            "external",
			ExternalIdList:  []string{group.AzureID},
			Audience:        v.AllStaff.DependsOn,
			AvailableGroups: []uuid.UUID{v.Approver.ID, v.Editor.ID, v.PermissionManager.ID},
			// Audience required:
			// AvailableGroups required:
		}
		SiteGroups[siteId] = v
	}
}
func NoPermissions(sites map[string]uuid.UUID, SiteGroups SiteGroupMap) {
	for _, group := range LoadNoPermissions() {
		if len(group.ExternalID) == 0 {
			continue
		}
		siteId, ok := sites[strings.ToLower(group.Site)]
		if !ok {
			fmt.Printf("\nfailed to find siteId for name: [%s]", group.Site)
			continue
		}
		v, _ := SiteGroups[siteId]
		v.AllStaff = identity.SecurityGroup{
			ID:             utils.GenerateSatoriUUIDFromString(group.Site + group.ExternalID + "all-staff"),
			Name:           group.GroupName,
			Description:    "All Staff / No Permissions",
			RoleID:         NoPermissionRoleID,
			SiteID:         &siteId,
			Active:         true,
			Type:           "external",
			ExternalIdList: []string{group.ExternalID},
		}
		SiteGroups[siteId] = v
		//groups = append(groups, commonModels.SecurityGroup{
		//	ID:             utils.GenerateSatoriUUIDFromString(group.Site + group.ExternalID + "all-staff"),
		//	Name:           group.GroupName,
		//	Description:    "All Staff / No Permissions",
		//	RoleID:         NoPermissionRoleID,
		//	SiteID:         &siteId,
		//	Active:         true,
		//	Type:           "external",
		//	ExternalIdList: []string{group.ExternalID},
		//})
	}
}
func ManualGroups(SiteGroups SiteGroupMap) {
	for siteId, _ := range SiteGroups {
		dr := uuid.FromStringOrNil(siteId.String())
		x := siteId.String()
		log.Println(x)
		v, _ := SiteGroups[dr]
		dependsOn := SiteGroups[dr].AllStaff.ExternalIdList
		v.Approver = identity.SecurityGroup{
			ID:          utils.GenerateSatoriUUIDFromString(dr.String() + "approver"),
			Name:        "Approvers",
			Description: "Approver Group (Manual)",
			RoleID:      NoPermissionRoleID,
			SiteID:      &dr,
			Active:      true,
			Type:        "manual",
			DependsOn:   dependsOn,
		}
		v.Editor = identity.SecurityGroup{
			ID:          utils.GenerateSatoriUUIDFromString(dr.String() + "editor"),
			Name:        "Editors",
			Description: "Editor's Group (Manual)",
			RoleID:      NoPermissionRoleID,
			SiteID:      &dr,
			Active:      true,
			Type:        "manual",
			DependsOn:   dependsOn,
		}
		permissionManagerID := utils.GenerateSatoriUUIDFromString(dr.String() + "permissionManager")
		v.PermissionManager = identity.SecurityGroup{
			ID:              permissionManagerID,
			Name:            "Permission Managers",
			Description:     "Permission Manager's Group (Manual)",
			RoleID:          NoPermissionRoleID,
			SiteID:          &dr,
			Active:          true,
			Type:            "manual",
			DependsOn:       dependsOn,
			Audience:        dependsOn,
			AvailableGroups: []uuid.UUID{v.Approver.ID, v.Editor.ID, permissionManagerID},
		}
		x = v.PermissionManager.SiteID.String()
		SiteGroups[siteId] = v
	}
}
func TenantWideGroups() identity.SecurityGroups {
	return append(identity.SecurityGroups{}, identity.SecurityGroup{
		Name:           "CBE - Program Team",
		Description:    "CBE - Program Team",
		RoleID:         NoPermissionRoleID,
		SiteID:         nil,
		Active:         true,
		Type:           "external",
		ExternalIdList: []string{"b54961b0-c877-4738-8b0c-239d5172a0d9"},
	})
}
func Users(sites map[string]uuid.UUID, groupMap SiteGroupMap) []identity.Account {
	var users = make([]identity.Account, 0)
	var emails = map[string]identity.Account{}

	for _, user := range LoadUsersToElevate() {
		siteId, ok := sites[user.School]
		if !ok {
			fmt.Printf("failed to find school: [%s] for user: [%s]", user.School, user.Name)
			continue
		}
		if len(user.Email) == 0 {
			if len(user.Name) == 0 {
				continue
			}
			parts := strings.Split(user.Name, " ")
			if len(parts) < 2 {
				fmt.Printf("user missing any valid name: [%s - %s - %s - %s]", user.FirstName, user.LastName, user.Name, user.Group)
				continue
			}
			user.Email = strings.ToLower(user.FirstName[:2] + user.LastName + "@cbe.ab.ca")
			user.FirstName = parts[0]
			user.LastName = parts[1]

		}
		account, ok := emails[user.Email]
		account.Firstname = user.FirstName
		account.Lastname = user.LastName
		account.Email = user.Email
		account.Password = ""
		account.Settings = json.RawMessage{}
		account.PrivacyLevel = 2
		account.Active = true

		switch user.Group {
		case "approver":
			account.ManualGroups = append(account.ManualGroups, groupMap[siteId].Approver.ID)
		case "editor":
			account.ManualGroups = append(account.ManualGroups, groupMap[siteId].Editor.ID)
		case "administrator":
			account.ManualGroups = append(account.ManualGroups, groupMap[siteId].PermissionManager.ID)
		}
		emails[user.Email] = account
	}
	for _, account := range emails {
		users = append(users, account)
	}
	return users
}
