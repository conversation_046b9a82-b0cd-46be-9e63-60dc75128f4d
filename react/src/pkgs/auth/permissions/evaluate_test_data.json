[{"Description": "should return true for admin accounts", "Authorizable": {"IsAdmin": true, "Groups": []}, "Secured": {"Sites": null, "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "create", "ActionResult": true, "ScopeEntityResult": true}, {"Description": "should return false for non-admin accounts without groups", "Authorizable": {"IsAdmin": false, "Groups": []}, "Secured": {"Sites": null, "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "create", "ActionResult": false, "ScopeEntityResult": false}, {"Description": "different results for Evaluate/EvaluateEntityScopeOnly", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": "********-0000-0000-0000-************", "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": ["********-0000-0000-0000-************"], "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "create", "ActionResult": false, "ScopeEntityResult": true}, {"Description": "Departments: different results", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": "********-0000-0000-0000-************", "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": ["********-0000-0000-0000-************", "********-0000-0000-0000-************"], "DepartmentID": "********-0000-0000-0000-************", "EntityScope": "cm.settings.department"}, "Action": "create", "ActionResult": false, "ScopeEntityResult": true}, {"Description": "Tenant-wide: different results", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": null, "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": null, "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "create", "ActionResult": false, "ScopeEntityResult": true}, {"Description": "Tenant-wide: should pass permissions check", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": null, "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": null, "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "update", "ActionResult": true, "ScopeEntityResult": true}, {"Description": "Tenant-wide: should pass for site-specific permissions check", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": null, "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": ["********-0000-0000-0000-************", "********-0000-0000-0000-************"], "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "update", "ActionResult": true, "ScopeEntityResult": true}, {"Description": "Site-specific: shouldn't pass for tenant-wide permissions check", "Authorizable": {"IsAdmin": false, "Groups": [{"SiteID": "********-0000-0000-0000-************", "Scopes": ["cm.settings*/update"]}]}, "Secured": {"Sites": null, "DepartmentID": null, "EntityScope": "cm.settings.department"}, "Action": "update", "ActionResult": false, "ScopeEntityResult": false}]