-- Revert cm_tenant_db_RebaseSchema:account_content_importer from pg

BEGIN;

--     IF EXISTS ( SELECT 1 FROM account WHERE id = '45f06f48-a93c-414e-b9a0-7582e0abc085') THEN
-- --      Update Owner fields to Null
--         UPDATE content SET owner = null WHERE owner = '45f06f48-a93c-414e-b9a0-7582e0abc085';
-- --      Update Owner fields to Null
--         UPDATE content SET publisher = null WHERE publisher = '45f06f48-a93c-414e-b9a0-7582e0abc085';
-- --      Delete Account
--         DELETE FROM account WHERE id = '45f06f48-a93c-414e-b9a0-7582e0abc085' and email = '<EMAIL>';
--     FROM END IF;

COMMIT;
