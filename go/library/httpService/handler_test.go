package httpService

import (
	utils "contentmanager/tests/test_utils"
	"net/http"
	"net/http/httptest"
	"testing"
)

func Test_New(t *testing.T) {
	m := New()
	if m == nil {
		t.Error("middleware.New() cannot return nil")
	}
}

func Test_Middleware_ServeHTTP(t *testing.T) {
	result := ""
	response := httptest.NewRecorder()

	m := New()
	m.Use(func(c Context) {
		result += "foo"
		c.Next()
		result += "ban"
	})
	m.Use(func(c Context) {
		result += "bar"
		c.Next()
		result += "baz"
	})
	m.Action(func(res http.ResponseWriter, req *http.Request) {
		result += "bat"
		res.WriteHeader(http.StatusBadRequest)
	})

	m.ServeHTTP(response, &http.Request{})

	utils.Expect(t, result, "foobarbatbazban")
	utils.Expect(t, response.Code, http.StatusBadRequest)
}

func Test_Middleware_Handlers(t *testing.T) {
	result := ""
	response := httptest.NewRecorder()

	batman := func(c Context) {
		result += "batman!"
	}

	m := New()
	m.Use(func(c Context) {
		result += "foo"
		c.Next()
		result += "ban"
	})
	m.Handlers(
		batman,
		batman,
		batman,
	)
	m.Action(func(res http.ResponseWriter, req *http.Request) {
		result += "bat"
		res.WriteHeader(http.StatusBadRequest)
	})

	m.ServeHTTP(response, &http.Request{})

	utils.Expect(t, result, "batman!batman!batman!bat")
	utils.Expect(t, response.Code, http.StatusBadRequest)
}

func Test_Middleware_EarlyWrite(t *testing.T) {
	result := ""
	response := httptest.NewRecorder()

	m := New()
	m.Use(func(res http.ResponseWriter) {
		result += "foobar"
		res.Write([]byte("Hello world"))
	})
	m.Use(func() {
		result += "bat"
	})
	m.Action(func(res http.ResponseWriter) {
		result += "baz"
		res.WriteHeader(http.StatusBadRequest)
	})

	m.ServeHTTP(response, &http.Request{})

	utils.Expect(t, result, "foobar")
	utils.Expect(t, response.Code, http.StatusOK)
}

func Test_Middleware_Written(t *testing.T) {
	response := httptest.NewRecorder()

	m := New()
	m.Handlers(func(res http.ResponseWriter) {
		res.WriteHeader(http.StatusOK)
	})

	ctx := m.createContext(response, &http.Request{})
	utils.Expect(t, ctx.Written(), false)

	ctx.run()
	utils.Expect(t, ctx.Written(), true)
}

func Test_Middleware_Basic_NoRace(t *testing.T) {
	m := New()
	handlers := []Handler{func() {}, func() {}}
	// Ensure append will not realloc to trigger the race condition
	m.handlers = handlers[:1]
	req, _ := http.NewRequest("GET", "/", nil)
	for i := 0; i < 2; i++ {
		go func() {
			response := httptest.NewRecorder()
			m.ServeHTTP(response, req)
		}()
	}
}
