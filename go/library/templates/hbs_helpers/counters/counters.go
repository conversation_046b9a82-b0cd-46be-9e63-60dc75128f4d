package counters

import (
	"fmt"
	"strings"
)

type Counters struct {
	cm map[string]int
}

func NewCounters() *Counters {
	return &Counters{
		cm: map[string]int{},
	}
}

func (c *Counters) Get(name string) int {
	return c.cm[strings.ToLower(name)]
}

func (c *Counters) GetAndInc(name string) int {
	prev := c.cm[strings.ToLower(name)]
	c.Inc(name)
	return prev
}

func (c *Counters) Inc(name string) {
	c.cm[strings.ToLower(name)] += 1
}

func (c *Counters) Compare(name string, sign string, num int) bool {
	v := c.cm[strings.ToLower(name)]
	switch sign {
	case "<":
		return v < num
	case "<=", "=<":
		return v <= num
	case ">":
		return v > num
	case ">=", "=>":
		return v >= num
	case "=", "==", "===":
		return v == num
	case "!=", "!==", "<>", "><":
		return v != num
	case "%":
		return v%num == 0
	}
	fmt.Printf("Invalid `sign` value for comparision: %s\n", sign)
	return false
}
