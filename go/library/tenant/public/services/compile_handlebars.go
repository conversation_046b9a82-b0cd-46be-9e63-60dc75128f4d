package publicServices

import (
	"contentmanager/etc/conf"
	"contentmanager/library/helpers/ietags"
	"contentmanager/library/shared"
	"contentmanager/library/templates/hbs_helpers"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/tenant/public/features/content"
	"contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/tenant/public/viewmodels"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/content/public"
	"contentmanager/pkgs/htmlxx"
	"encoding/json"
	"github.com/satori/go.uuid"
	"strings"
)

type CompileHandlebarsParams struct {
	ContentChain []publicModels.ContentForHandlebars
	ContentOnly  bool // ignore templates, use only content.content (content.ContentStructure.Template)
}

func CompileHandlebars(r *shared.AppContext, config CompileHandlebarsParams) (string, error) {
	var err error
	contentChain := config.ContentChain
	dbCon := r.TenantDatabase()
	host := r.Request().Host
	rUrl := r.Request().URL
	siteId := r.CurrentSiteID()
	tenantId := r.TenantID()
	site := r.CurrentSite()
	account := r.PublicAccount()
	flattened := contentChain[len(contentChain)-1]

	helpers := hbsHelpers.NewHbsHelpers(r)
	results := flattened.Content

	var masterClassifiedContent []publicModels.ContentForHandlebars
	var eventContent []publicModels.ContentForHandlebars
	var taggedPages = []publicModels.ContentForHandlebars{}

	var primaryNavigation publicModels.HandlebarsPrimaryNavigationChain
	var newsContent []publicModels.ContentForHandlebars
	var sharedNewsContent []publicModels.ContentForHandlebars
	var siteNewsContent []publicModels.ContentForHandlebars
	var dctContent json.RawMessage
	var navigationItems = []publicModels.ContentForHandlebars{}
	var breadcrumbs []publicModels.ContentForHandlebars
	var navigationContent = publicModels.ContentForHandlebars{}
	var navigationParent = publicModels.ContentForHandlebars{Title: "Home", Route: "/"}
	var contentTags []commonModels.Tag
	var eventTags []commonModels.Tag
	var distributedPage string
	var seo viewmodels.Seo
	var contentTitle string
	var transportationAlert bool

	if len(host) > 0 {
		seo.SiteURL = host
	}
	if rUrl != nil && len(rUrl.Path) > 0 {
		seo.Path = rUrl.Path
	}

	content := flattened.Content

	content, err = AzureLoginReplacer(content, r.Request(), helpers)
	if err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("Error executing azure login template")
	}

	if strings.Contains(content, conf.UseTemplateContentHandlebar) {
		masterClassifiedContent, _ = publiccontent.GetByParams(dbCon, publiccontent.Params{}.
			WithSiteId(siteId).
			WithClassification(string(commonModels.Template)).
			WithAllPages())
	}

	if strings.Contains(content, conf.EventContentHandlebar) {
		eventContent, _ = publiccontent.GetByParams(dbCon, publiccontent.Params{}.
			WithSiteId(siteId).
			WithPublishedOnly().
			WithPrivacyLevel(account.PrivacyLevel).
			WithContentTypes(publiccontent.Event).
			WithSort(publiccontent.Sort{
				Field: "startdate",
				Order: "asc",
			}).
			WithConsiderUpcomingDate().
			WithConsiderExpirationDate().
			WithTags().
			WithAllPages())
	}

	if strings.Contains(content, conf.TransportationAlertHandlebar) {
		useSiteId := site.Type != "central office"
		if v, e := commonServices.GetBusStatusResults(dbCon, uuid.NullUUID{UUID: siteId, Valid: true}, useSiteId, true); e == nil && len(v) > 0 {
			transportationAlert = true
		}
	}

	dctContent = flattened.Data

	contentTitle = flattened.Title
	flattened.EditLink = flattened.GenerateEditLink(r.Tenant().AdminURL, r.CurrentSiteID())

	if flattened.Type != commonModels.JS && flattened.Type != commonModels.CSS {
		if !config.ContentOnly {
			// Navigation
			navigationChain := GetSiteNavigation(dbCon, siteId, r.PublicAccount().PrivacyLevel)
			navigationContext := findSelf(navigationChain, flattened)
			if navigationContext.Self != nil {
				navigationItems = navigationContext.Self.Children
			}
			navigationParent = navigationContext.Parent
			breadcrumbs = publicModels.BuildBreadcrumbs(navigationChain, flattened.ID)
			primaryNavigation = findPrimaryNavigation(navigationChain)

			// Deprecated - use Query Instead
			// siteNewsContent is currently used at CSSD to determine if a school has any news in hbs if/else
			newsContent, sharedNewsContent, siteNewsContent, _ = GetNews(dbCon, siteId, account.PrivacyLevel)
			// Deprecated - Use Query instead
			// Used in hbs filtering (e.g #RenderWithTag)
			contentTags, _ = GetAndCacheForContent(dbCon, tenantId)
			// Used in Calendar pages
			eventTags, _ = GetAndCacheTagsWithUseForEvents(dbCon, siteId)
			// Distributed Pages - Use isDistrictPage flag to determine whether to query for descendants or not.
		}

		var settingsMap = utils.BytesToMapStringInterface(flattened.Settings)
		if isDistrictPage, _ := utils.GetValueFromJSONMap[bool](settingsMap, "isDistrictPage"); isDistrictPage {
			distributedPage = public.GetDistributedPageContent(r, flattened.ID, siteId)
		}
	}

	if flattened.PrivacyLevel&account.PrivacyLevel != contentChain[len(contentChain)-1].PrivacyLevel {
		content = strings.Replace(content, content, AccessDeniedSource, 1)
	}

	contentTemplate, err := handlebars.Parse(content)
	if err != nil {
		return "", err
	}

	baseViewModel := &viewmodels.BaseContentPage{
		Timezone:                r.Timezone(),
		Account:                 &account,
		Content:                 flattened,
		Breadcrumbs:             breadcrumbs,
		Site:                    r.CurrentSiteViewModel(),
		News:                    newsContent,
		Events:                  eventContent,
		SharedNews:              sharedNewsContent,
		SiteNews:                siteNewsContent,
		Seo:                     seo,
		Dct:                     dctContent,
		DctMap:                  utils.BytesToMapStringInterface(dctContent),
		NavigationContent:       navigationContent,
		CurrentTitle:            contentTitle,
		CurrentContent:          flattened,
		NavigationItems:         navigationItems,
		NavigationParent:        navigationParent,
		MasterClassifiedContent: masterClassifiedContent,
		TransportationAlert:     transportationAlert,
		TaggedPages:             taggedPages,
		ContentTags:             contentTags,
		PrimaryNavigation:       primaryNavigation,
		EventTags:               eventTags,
		DistributedContent:      distributedPage,
	}

	content, err = contentTemplate.ExecWithHelpers(baseViewModel, helpers)
	if err != nil {
		return "", err
	}

	if strings.Contains(flattened.Content, conf.UseTemplateContentHandlebar) {
		if contentTemplate, err = handlebars.Parse(content); err == nil {
			content, err = contentTemplate.ExecWithHelpers(baseViewModel, helpers)
			if err != nil {
				return "", err
			}
		}
	}

	if err != nil {
		return "", err
	}
	results = content
	results = ietags.ReplaceIETags(r, results)
	if r.Workspace() != "live" && flattened.Type != commonModels.JS && flattened.Type != commonModels.CSS {
		results = htmlxx.AppendQueryParamsToURLs(results, map[string]string{"ie_workspace": r.Workspace()})
	}
	return results, err
}
